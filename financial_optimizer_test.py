#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金融时间序列优化器测试脚本
基于P.pull.py的模型架构进行优化器性能对比测试
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import tensorflow as tf
from datetime import datetime
from typing import Tuple, Dict, Any
import json

# 导入测试框架
from optimizer_test_framework import OptimizerTestFramework, DEFAULT_TEST_CONFIG

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class FinancialOptimizerTest:
    """金融时间序列优化器测试类"""
    
    def __init__(self, data_config: Dict[str, Any]):
        """
        初始化测试类
        
        Args:
            data_config: 数据配置字典
        """
        self.data_config = data_config
        self.test_framework = None
        
    def create_simple_financial_model(self, input_shape: Tuple[int, int], 
                                     output_dim: int = 1) -> tf.keras.Model:
        """
        创建简化的金融时间序列预测模型
        基于P.pull.py的架构但简化以便快速测试
        """
        # 输入层
        inputs = tf.keras.layers.Input(shape=input_shape, name='input_layer')
        
        # 特征提取层
        x = tf.keras.layers.Dense(64, activation='relu', name='feature_dense_1')(inputs)
        x = tf.keras.layers.Dropout(0.2)(x)
        x = tf.keras.layers.Dense(32, activation='relu', name='feature_dense_2')(x)
        x = tf.keras.layers.Dropout(0.2)(x)
        
        # LSTM层（简化版本）
        x = tf.keras.layers.Reshape((-1, 32))(x)  # 重塑为序列格式
        x = tf.keras.layers.LSTM(64, return_sequences=True, name='lstm_1')(x)
        x = tf.keras.layers.Dropout(0.3)(x)
        x = tf.keras.layers.LSTM(32, return_sequences=False, name='lstm_2')(x)
        x = tf.keras.layers.Dropout(0.3)(x)
        
        # 输出层
        outputs = tf.keras.layers.Dense(output_dim, activation='linear', name='output')(x)
        
        model = tf.keras.Model(inputs=inputs, outputs=outputs, name='financial_model')
        
        return model
    
    def generate_synthetic_financial_data(self, n_samples: int = 10000, 
                                         n_features: int = 20, 
                                         sequence_length: int = 60) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成合成的金融时间序列数据用于测试
        模拟股票价格、技术指标等特征
        """
        logging.info(f"生成合成金融数据: {n_samples}样本, {n_features}特征, {sequence_length}序列长度")
        
        # 设置随机种子确保可重复性
        np.random.seed(42)
        
        # 生成基础价格序列（随机游走 + 趋势 + 周期性）
        base_prices = np.cumsum(np.random.randn(n_samples + sequence_length) * 0.02) + 100
        
        # 添加趋势和周期性
        trend = np.linspace(0, 10, n_samples + sequence_length)
        seasonal = 5 * np.sin(2 * np.pi * np.arange(n_samples + sequence_length) / 252)  # 年度周期
        prices = base_prices + trend + seasonal
        
        # 生成技术指标特征
        features_list = []
        
        for i in range(n_samples):
            sample_features = []
            
            # 价格相关特征
            price_window = prices[i:i+sequence_length]
            sample_features.extend([
                np.mean(price_window),  # 均价
                np.std(price_window),   # 价格波动率
                (price_window[-1] - price_window[0]) / price_window[0],  # 收益率
                np.max(price_window) - np.min(price_window),  # 价格区间
            ])
            
            # 技术指标（简化版本）
            # 移动平均
            ma_5 = np.mean(price_window[-5:])
            ma_20 = np.mean(price_window[-20:]) if len(price_window) >= 20 else np.mean(price_window)
            sample_features.extend([ma_5, ma_20, ma_5 - ma_20])
            
            # RSI简化版本
            price_changes = np.diff(price_window)
            gains = np.where(price_changes > 0, price_changes, 0)
            losses = np.where(price_changes < 0, -price_changes, 0)
            avg_gain = np.mean(gains) if len(gains) > 0 else 0
            avg_loss = np.mean(losses) if len(losses) > 0 else 0.001
            rsi = 100 - (100 / (1 + avg_gain / avg_loss))
            sample_features.append(rsi)
            
            # 成交量相关（模拟）
            volume = np.random.lognormal(10, 0.5)
            sample_features.append(volume)
            
            # 市场情绪指标（模拟）
            market_sentiment = np.random.normal(0, 1)
            sample_features.append(market_sentiment)
            
            # 填充到指定特征数量
            while len(sample_features) < n_features:
                # 添加噪声特征
                sample_features.append(np.random.normal(0, 1))
            
            # 截断到指定特征数量
            sample_features = sample_features[:n_features]
            features_list.append(sample_features)
        
        X = np.array(features_list, dtype=np.float32)
        
        # 生成目标变量（下一期收益率）
        future_returns = []
        for i in range(n_samples):
            current_price = prices[i + sequence_length - 1]
            future_price = prices[i + sequence_length]
            return_rate = (future_price - current_price) / current_price
            future_returns.append(return_rate)
        
        y = np.array(future_returns, dtype=np.float32)
        
        # 标准化特征
        X_mean = np.mean(X, axis=0)
        X_std = np.std(X, axis=0) + 1e-8  # 避免除零
        X = (X - X_mean) / X_std
        
        # 标准化目标变量
        y_mean = np.mean(y)
        y_std = np.std(y) + 1e-8
        y = (y - y_mean) / y_std
        
        logging.info(f"数据生成完成: X.shape={X.shape}, y.shape={y.shape}")
        logging.info(f"特征统计: mean={np.mean(X):.4f}, std={np.std(X):.4f}")
        logging.info(f"目标统计: mean={np.mean(y):.4f}, std={np.std(y):.4f}")
        
        return X, y
    
    def prepare_test_data(self) -> Tuple[Tuple[np.ndarray, np.ndarray], Tuple[np.ndarray, np.ndarray]]:
        """准备训练和验证数据"""
        # 生成数据
        X, y = self.generate_synthetic_financial_data(
            n_samples=self.data_config.get('n_samples', 10000),
            n_features=self.data_config.get('n_features', 20),
            sequence_length=self.data_config.get('sequence_length', 60)
        )
        
        # 分割训练和验证集
        split_idx = int(len(X) * 0.8)
        
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        logging.info(f"数据分割完成:")
        logging.info(f"  训练集: X_train.shape={X_train.shape}, y_train.shape={y_train.shape}")
        logging.info(f"  验证集: X_val.shape={X_val.shape}, y_val.shape={y_val.shape}")
        
        return (X_train, y_train), (X_val, y_val)
    
    def run_optimizer_comparison(self, optimizer_list: list = None) -> Dict[str, Any]:
        """运行优化器对比测试"""
        if optimizer_list is None:
            optimizer_list = ['adam', 'adamw', 'lion', 'ademamix', 'fractional', 'lookahead_adam']
        
        logging.info("开始金融时间序列优化器对比测试")
        
        # 准备数据
        train_data, val_data = self.prepare_test_data()
        
        # 创建测试配置
        test_config = DEFAULT_TEST_CONFIG.copy()
        test_config.update({
            'learning_rate': 1e-4,
            'batch_size': 64,
            'epochs': 100,
            'loss': 'mse',
            'metrics': ['mae', 'mse'],
            'test_name': 'financial_time_series_optimizer_comparison'
        })
        
        # 初始化测试框架
        self.test_framework = OptimizerTestFramework(test_config)
        
        # 定义模型构建函数
        def model_builder():
            input_shape = (train_data[0].shape[1],)  # 特征维度
            return self.create_simple_financial_model(input_shape, output_dim=1)
        
        # 运行测试
        results = self.test_framework.run_comprehensive_test(
            model_builder, train_data, val_data, optimizer_list
        )
        
        # 生成金融特定的分析报告
        financial_analysis = self.analyze_financial_performance(results)
        results['financial_analysis'] = financial_analysis
        
        return results
    
    def analyze_financial_performance(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析金融性能指标"""
        successful_tests = {k: v for k, v in results.items() 
                           if isinstance(v, dict) and v.get('success', False)}
        
        if not successful_tests:
            return {'error': '没有成功的测试结果'}
        
        analysis = {
            'risk_adjusted_performance': {},
            'convergence_analysis': {},
            'stability_analysis': {},
            'efficiency_analysis': {}
        }
        
        # 风险调整后的性能分析
        for name, result in successful_tests.items():
            val_loss = result['final_val_loss']
            stability = result['val_loss_stability']
            
            # 计算风险调整后的性能（类似夏普比率的概念）
            risk_adjusted_score = -val_loss / (stability + 1e-8)  # 负损失除以波动率
            analysis['risk_adjusted_performance'][name] = risk_adjusted_score
        
        # 收敛分析
        for name, result in successful_tests.items():
            analysis['convergence_analysis'][name] = {
                'convergence_speed': result['convergence_speed'],
                'convergence_epoch': result['convergence_epoch'],
                'final_performance': result['final_val_loss']
            }
        
        # 稳定性分析
        for name, result in successful_tests.items():
            analysis['stability_analysis'][name] = {
                'val_loss_stability': result['val_loss_stability'],
                'memory_efficiency': result['memory_delta_mb']
            }
        
        # 效率分析
        for name, result in successful_tests.items():
            training_time = result['training_time_seconds']
            final_loss = result['final_val_loss']
            efficiency_score = -final_loss / (training_time + 1e-8)  # 性能/时间
            analysis['efficiency_analysis'][name] = {
                'training_time': training_time,
                'efficiency_score': efficiency_score
            }
        
        # 推荐最佳优化器（综合考虑多个因素）
        best_risk_adjusted = max(analysis['risk_adjusted_performance'].keys(),
                                key=lambda x: analysis['risk_adjusted_performance'][x])
        best_efficiency = max(analysis['efficiency_analysis'].keys(),
                             key=lambda x: analysis['efficiency_analysis'][x]['efficiency_score'])
        
        analysis['recommendations'] = {
            'best_risk_adjusted': best_risk_adjusted,
            'best_efficiency': best_efficiency,
            'summary': f"风险调整后最佳: {best_risk_adjusted}, 效率最佳: {best_efficiency}"
        }
        
        return analysis
    
    def save_results(self, results: Dict[str, Any], output_dir: str = None):
        """保存测试结果"""
        if output_dir is None:
            output_dir = f"financial_optimizer_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存完整结果
        results_file = os.path.join(output_dir, 'financial_optimizer_results.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存推荐报告
        if 'financial_analysis' in results:
            recommendations = results['financial_analysis'].get('recommendations', {})
            report_file = os.path.join(output_dir, 'optimizer_recommendations.txt')
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("金融时间序列优化器测试报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                if recommendations:
                    f.write("推荐结果:\n")
                    f.write(f"风险调整后最佳优化器: {recommendations.get('best_risk_adjusted', 'N/A')}\n")
                    f.write(f"效率最佳优化器: {recommendations.get('best_efficiency', 'N/A')}\n")
                    f.write(f"总结: {recommendations.get('summary', 'N/A')}\n\n")
                
                # 详细性能数据
                successful_tests = {k: v for k, v in results.items() 
                                   if isinstance(v, dict) and v.get('success', False)}
                
                f.write("详细性能数据:\n")
                f.write("-" * 30 + "\n")
                for name, result in successful_tests.items():
                    f.write(f"\n{name}:\n")
                    f.write(f"  最终验证损失: {result['final_val_loss']:.6f}\n")
                    f.write(f"  收敛速度: {result['convergence_speed']} epochs\n")
                    f.write(f"  训练时间: {result['training_time_seconds']:.2f} 秒\n")
                    f.write(f"  稳定性: {result['val_loss_stability']:.6f}\n")
                    f.write(f"  内存使用: {result['memory_delta_mb']:.2f} MB\n")
        
        logging.info(f"测试结果已保存到: {output_dir}")
        return output_dir


def main():
    """主函数"""
    # 数据配置
    data_config = {
        'n_samples': 8000,      # 样本数量
        'n_features': 25,       # 特征数量
        'sequence_length': 60   # 序列长度
    }
    
    # 要测试的优化器列表
    optimizer_list = ['adam', 'adamw', 'lion', 'ademamix', 'fractional']
    
    # 创建测试实例
    test_instance = FinancialOptimizerTest(data_config)
    
    # 运行测试
    logging.info("开始金融时间序列优化器对比测试")
    results = test_instance.run_optimizer_comparison(optimizer_list)
    
    # 保存结果
    output_dir = test_instance.save_results(results)
    
    # 打印简要结果
    if 'financial_analysis' in results and 'recommendations' in results['financial_analysis']:
        recommendations = results['financial_analysis']['recommendations']
        print("\n" + "="*60)
        print("测试完成！推荐结果:")
        print(f"风险调整后最佳优化器: {recommendations.get('best_risk_adjusted', 'N/A')}")
        print(f"效率最佳优化器: {recommendations.get('best_efficiency', 'N/A')}")
        print(f"详细结果保存在: {output_dir}")
        print("="*60)


if __name__ == "__main__":
    main()
