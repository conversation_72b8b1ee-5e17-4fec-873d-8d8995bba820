2025-08-04 11:48:19,455 - INFO - 开始金融时间序列优化器对比测试
2025-08-04 11:48:19,455 - INFO - 开始金融时间序列优化器对比测试
2025-08-04 11:48:19,455 - INFO - 生成合成金融数据: 8000样本, 25特征, 60序列长度
2025-08-04 11:48:21,232 - INFO - 数据生成完成: X.shape=(8000, 25), y.shape=(8000,)
2025-08-04 11:48:21,233 - INFO - 特征统计: mean=0.0000, std=1.0000
2025-08-04 11:48:21,233 - INFO - 目标统计: mean=0.0000, std=1.0000
2025-08-04 11:48:21,238 - INFO - 数据分割完成:
2025-08-04 11:48:21,239 - INFO -   训练集: X_train.shape=(6400, 25), y_train.shape=(6400,)
2025-08-04 11:48:21,239 - INFO -   验证集: X_val.shape=(1600, 25), y_val.shape=(1600,)
2025-08-04 11:48:21,239 - INFO - 优化器测试框架初始化完成，结果将保存到: optimizer_test_results_20250804_114821
2025-08-04 11:48:21,239 - INFO - 开始全面测试，优化器列表: ['adam', 'adamw', 'lion', 'ademamix', 'fractional']
2025-08-04 11:48:21,239 - INFO - 开始测试优化器: adam
2025-08-04 11:49:27,203 - INFO - 优化器 adam 测试完成:
2025-08-04 11:49:27,204 - INFO -   最终验证损失: 0.073805
2025-08-04 11:49:27,204 - INFO -   收敛epoch: 76
2025-08-04 11:49:27,204 - INFO -   训练时间: 65.61秒
2025-08-04 11:49:27,204 - INFO -   内存增长: 88.69MB
2025-08-04 11:49:32,537 - INFO - 开始测试优化器: adamw
2025-08-04 11:50:49,252 - INFO - 优化器 adamw 测试完成:
2025-08-04 11:50:49,252 - INFO -   最终验证损失: 0.068679
2025-08-04 11:50:49,252 - INFO -   收敛epoch: 100
2025-08-04 11:50:49,252 - INFO -   训练时间: 76.40秒
2025-08-04 11:50:49,252 - INFO -   内存增长: 44.33MB
2025-08-04 11:50:54,623 - INFO - 开始测试优化器: lion
2025-08-04 11:50:55,054 - ERROR - 优化器 lion 测试失败: BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'
2025-08-04 11:51:00,374 - INFO - 开始测试优化器: ademamix
2025-08-04 11:51:00,790 - ERROR - 优化器 ademamix 测试失败: BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'
2025-08-04 11:51:06,108 - INFO - 开始测试优化器: fractional
2025-08-04 11:51:06,530 - ERROR - 优化器 fractional 测试失败: BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'
2025-08-04 11:51:11,869 - INFO - 测试结果已保存到: optimizer_test_results_20250804_114821/test_results.json
2025-08-04 11:51:11,869 - INFO - CSV摘要已保存到: optimizer_test_results_20250804_114821/optimizer_summary.csv
2025-08-04 11:51:16,431 - INFO - 可视化报告已保存到: optimizer_test_results_20250804_114821/optimizer_comparison_chart.png
2025-08-04 11:51:16,434 - INFO - 测试结果已保存到: financial_optimizer_test_20250804_115116
