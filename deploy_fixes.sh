#!/bin/bash
# 部署修复后的文件到云服务器并测试

echo "🚀 部署修复后的文件到云服务器"
echo "=================================="

# 配置变量
SERVER_IP="***************"
KEY_PATH="/Users/<USER>/Downloads/P.pem"
REMOTE_USER="ubuntu"
REMOTE_PATH="/home/<USER>"

# 检查密钥文件
if [ ! -f "$KEY_PATH" ]; then
    echo "❌ 密钥文件不存在: $KEY_PATH"
    exit 1
fi

echo "📁 准备上传修复后的文件..."

# 要上传的文件列表
FILES=(
    "P.pull.py"
    "test_core_fixes.py"
)

# 检查文件是否存在
for file in "${FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 文件不存在: $file"
        exit 1
    fi
done

echo "✅ 所有文件检查完成"

# 上传文件
echo "📤 上传文件到云服务器..."
for file in "${FILES[@]}"; do
    echo "   上传 $file..."
    scp -i "$KEY_PATH" "$file" "$REMOTE_USER@$SERVER_IP:$REMOTE_PATH/"
    if [ $? -eq 0 ]; then
        echo "   ✅ $file 上传成功"
    else
        echo "   ❌ $file 上传失败"
        exit 1
    fi
done

echo "🎉 所有文件上传完成！"

# 创建远程测试脚本
echo "📝 创建远程测试脚本..."
cat > remote_test_fixes.sh << 'EOF'
#!/bin/bash
echo "🔧 在云服务器上测试修复效果..."
echo "=============================="

# 检查Python环境
echo "🐍 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查必要的包
echo "📦 检查Python包..."
python3 -c "import pandas, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 缺少必要的包，尝试安装..."
    pip3 install pandas numpy
fi

echo ""
echo "🚀 开始核心修复测试..."
echo "===================="
python3 test_core_fixes.py

echo ""
echo "📊 测试完成！"
echo "============"

# 检查是否有config.py和tushare
if [ -f "config.py" ]; then
    echo "🔍 检查Tushare配置..."
    python3 -c "
import sys
try:
    import tushare as ts
    from config import Config
    ts.set_token(Config.TUSHARE_TOKEN)
    pro = ts.pro_api()
    
    # 测试基础调用
    data = pro.daily(ts_code='000001.SZ', start_date='20241201', end_date='20241201')
    print('✅ Tushare配置正常')
    
    # 测试修复后的权限检查
    from P import check_tushare_permissions
    permissions = check_tushare_permissions()
    print('✅ 权限检查功能正常')
    
except ImportError as e:
    print(f'⚠️ 导入失败: {e}')
except Exception as e:
    print(f'⚠️ 测试失败: {e}')
"
else
    echo "⚠️ config.py 不存在，跳过Tushare测试"
fi

echo ""
echo "🎯 修复验证完成！"
echo "================"
echo "如果所有测试通过，说明修复成功："
echo "1. ✅ limit_step接口已移除，使用替代方案"
echo "2. ✅ astype错误已修复"
echo "3. ✅ 序列数据诊断功能正常"
echo "4. ✅ 特征计算修复完成"
EOF

# 上传测试脚本
echo "📤 上传测试脚本..."
scp -i "$KEY_PATH" remote_test_fixes.sh "$REMOTE_USER@$SERVER_IP:$REMOTE_PATH/"

# 执行远程测试
echo ""
echo "🔗 连接到云服务器并执行测试..."
echo "================================"

ssh -i "$KEY_PATH" "$REMOTE_USER@$SERVER_IP" << 'ENDSSH'
cd /home/<USER>
chmod +x remote_test_fixes.sh
./remote_test_fixes.sh
ENDSSH

echo ""
echo "🎯 部署和测试完成！"
echo "=================="
echo ""
echo "📋 修复总结："
echo "1. ✅ 移除了limit_step接口调用（权限不足）"
echo "2. ✅ 修复了'list' object has no attribute 'astype'错误"
echo "3. ✅ 改进了序列数据生成的诊断功能"
echo "4. ✅ 实现了连板特征计算的替代方案"
echo ""
echo "💡 如果测试通过，您的量化系统现在应该能够："
echo "   - 正常获取涨跌停数据"
echo "   - 使用替代方案处理连板数据"
echo "   - 避免astype类型错误"
echo "   - 提供更好的错误诊断信息"
