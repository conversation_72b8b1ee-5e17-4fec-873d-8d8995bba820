#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare API权限和频率限制测试脚本
测试新的频率控制器和权限检查功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主模块中的类和函数
from P import (
    TushareRateLimiter, 
    check_tushare_permissions, 
    get_alternative_limit_data,
    pro,
    rate_limiter
)

def test_rate_limiter():
    """测试频率控制器"""
    print("🔧 测试Tushare频率控制器...")
    
    # 测试基础数据获取
    print("\n1. 测试基础数据获取...")
    try:
        data = rate_limiter.safe_api_call(
            pro.daily, 
            'daily',
            ts_code='000001.SZ', 
            start_date='20240101', 
            end_date='20240105'
        )
        print(f"✅ 基础数据获取成功: {len(data)} 条记录")
    except Exception as e:
        print(f"❌ 基础数据获取失败: {e}")
    
    # 测试涨跌停数据获取
    print("\n2. 测试涨跌停数据获取...")
    try:
        limit_data = rate_limiter.safe_api_call(
            pro.stk_limit, 
            'stk_limit',
            trade_date='20240102'
        )
        print(f"✅ 涨跌停数据获取成功: {len(limit_data)} 条记录")
    except Exception as e:
        print(f"❌ 涨跌停数据获取失败: {e}")
    
    # 测试连板天梯数据获取
    print("\n3. 测试连板天梯数据获取...")
    try:
        step_data = rate_limiter.safe_api_call(
            pro.limit_step, 
            'limit_step',
            trade_date='20240102'
        )
        print(f"✅ 连板天梯数据获取成功: {len(step_data)} 条记录")
    except Exception as e:
        print(f"❌ 连板天梯数据获取失败: {e}")
        print("💡 这可能需要10000积分以上的权限")

def test_permission_check():
    """测试权限检查"""
    print("\n🔍 测试权限检查功能...")
    
    permissions = check_tushare_permissions()
    
    print("\n权限状态报告:")
    print(f"基础数据权限: {'✅' if permissions['basic_data'] else '❌'}")
    print(f"涨跌停数据权限: {'✅' if permissions['limit_data'] else '❌'}")
    print(f"特色数据权限: {'✅' if permissions['special_data'] else '❌'}")
    
    return permissions

def test_alternative_solution():
    """测试替代方案"""
    print("\n🔄 测试替代数据获取方案...")
    
    # 获取最近3天的数据作为测试
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
    
    try:
        alt_data = get_alternative_limit_data(start_date, end_date)
        print(f"✅ 替代方案获取成功: {len(alt_data)} 条记录")
        
        if not alt_data.empty:
            print("\n数据样本:")
            print(alt_data.head())
            
    except Exception as e:
        print(f"❌ 替代方案失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始Tushare API修复测试")
    print("=" * 50)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 1. 测试频率控制器
        test_rate_limiter()
        
        # 2. 测试权限检查
        permissions = test_permission_check()
        
        # 3. 如果特色数据权限不足，测试替代方案
        if not permissions.get('special_data', False):
            print("\n💡 检测到特色数据权限不足，测试替代方案...")
            test_alternative_solution()
        
        print("\n" + "=" * 50)
        print("🎉 测试完成！")
        
        # 给出建议
        print("\n📋 建议:")
        if not permissions.get('limit_data', False):
            print("❌ 涨跌停数据权限不足，请检查积分是否达到2000以上")
        
        if not permissions.get('special_data', False):
            print("⚠️ 连板天梯数据权限不足，需要10000积分以上")
            print("💡 可以使用替代方案获取类似数据")
        
        print("✅ 频率控制器已启用，将自动处理API调用限制")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
