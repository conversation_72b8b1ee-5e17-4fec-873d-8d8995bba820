#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云服务器优化器测试脚本
在云服务器上运行优化器性能对比测试
"""

import os
import sys
import logging
import subprocess
import time
import json
import shutil
from datetime import datetime
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'cloud_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class CloudServerTest:
    """云服务器测试管理器"""
    
    def __init__(self):
        self.server_ip = "***************"
        self.username = "ubuntu"
        self.key_path = "/Users/<USER>/Downloads/P.pem"
        self.remote_dir = "/home/<USER>/optimizer_test"
        self.local_files = [
            "optimizer_test_framework.py",
            "financial_optimizer_test.py",
            "cloud_server_test.py"
        ]
        
    def check_local_files(self):
        """检查本地文件是否存在"""
        missing_files = []
        for file in self.local_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            logging.error(f"缺少本地文件: {missing_files}")
            return False
        
        logging.info("本地文件检查完成")
        return True
    
    def upload_files_to_server(self):
        """上传文件到云服务器"""
        logging.info("开始上传文件到云服务器...")
        
        try:
            # 创建远程目录
            ssh_cmd = f"ssh -i {self.key_path} {self.username}@{self.server_ip}"
            mkdir_cmd = f"{ssh_cmd} 'mkdir -p {self.remote_dir}'"
            result = subprocess.run(mkdir_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode != 0:
                logging.error(f"创建远程目录失败: {result.stderr}")
                return False
            
            # 上传每个文件
            for file in self.local_files:
                scp_cmd = f"scp -i {self.key_path} {file} {self.username}@{self.server_ip}:{self.remote_dir}/"
                logging.info(f"上传文件: {file}")
                
                result = subprocess.run(scp_cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode != 0:
                    logging.error(f"上传文件 {file} 失败: {result.stderr}")
                    return False
                else:
                    logging.info(f"文件 {file} 上传成功")
            
            logging.info("所有文件上传完成")
            return True
            
        except Exception as e:
            logging.error(f"上传文件时出错: {str(e)}")
            return False
    
    def install_dependencies(self):
        """在云服务器上安装依赖"""
        logging.info("在云服务器上安装Python依赖...")
        
        try:
            ssh_cmd = f"ssh -i {self.key_path} {self.username}@{self.server_ip}"
            
            # 更新系统包
            update_cmd = f"{ssh_cmd} 'sudo apt update'"
            subprocess.run(update_cmd, shell=True, check=True)
            
            # 安装Python3和pip
            install_python_cmd = f"{ssh_cmd} 'sudo apt install -y python3 python3-pip python3-venv'"
            subprocess.run(install_python_cmd, shell=True, check=True)
            
            # 创建虚拟环境
            venv_cmd = f"{ssh_cmd} 'cd {self.remote_dir} && python3 -m venv optimizer_env'"
            subprocess.run(venv_cmd, shell=True, check=True)
            
            # 安装Python包
            pip_packages = [
                "tensorflow>=2.12.0",
                "numpy>=1.21.0",
                "pandas>=1.3.0",
                "matplotlib>=3.5.0",
                "seaborn>=0.11.0",
                "psutil>=5.8.0",
                "scikit-learn>=1.0.0"
            ]
            
            for package in pip_packages:
                pip_cmd = f"{ssh_cmd} 'cd {self.remote_dir} && source optimizer_env/bin/activate && pip install {package}'"
                logging.info(f"安装包: {package}")
                result = subprocess.run(pip_cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode != 0:
                    logging.warning(f"安装包 {package} 可能失败: {result.stderr}")
                else:
                    logging.info(f"包 {package} 安装成功")
            
            logging.info("依赖安装完成")
            return True
            
        except Exception as e:
            logging.error(f"安装依赖时出错: {str(e)}")
            return False
    
    def run_optimizer_test(self, test_config=None):
        """在云服务器上运行优化器测试"""
        logging.info("在云服务器上运行优化器测试...")

        try:
            ssh_cmd = f"ssh -i {self.key_path} {self.username}@{self.server_ip}"

            # 创建测试配置文件
            if test_config:
                # 将配置保存到本地临时文件，然后上传
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(test_config, f, indent=2)
                    temp_config_file = f.name

                # 上传配置文件
                scp_cmd = f"scp -i {self.key_path} {temp_config_file} {self.username}@{self.server_ip}:{self.remote_dir}/test_config.json"
                subprocess.run(scp_cmd, shell=True, check=True)

                # 删除临时文件
                os.unlink(temp_config_file)

            # 运行测试（直接使用python3，不需要虚拟环境）
            test_cmd = f"{ssh_cmd} 'cd {self.remote_dir} && python3 financial_optimizer_test.py'"
            logging.info("开始执行优化器测试...")

            # 使用实时输出
            process = subprocess.Popen(
                test_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )

            # 实时显示输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(f"[云服务器] {output.strip()}")
                    logging.info(f"云服务器输出: {output.strip()}")

            return_code = process.poll()

            if return_code == 0:
                logging.info("优化器测试完成")
                return True
            else:
                logging.error(f"优化器测试失败，返回码: {return_code}")
                return False

        except Exception as e:
            logging.error(f"运行测试时出错: {str(e)}")
            return False
    
    def download_results(self, local_results_dir=None):
        """从云服务器下载测试结果"""
        if local_results_dir is None:
            local_results_dir = f"cloud_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        os.makedirs(local_results_dir, exist_ok=True)
        
        logging.info(f"从云服务器下载测试结果到: {local_results_dir}")
        
        try:
            # 下载结果目录
            scp_cmd = f"scp -i {self.key_path} -r {self.username}@{self.server_ip}:{self.remote_dir}/financial_optimizer_test_* {local_results_dir}/"
            result = subprocess.run(scp_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode != 0:
                logging.warning(f"下载结果目录可能失败: {result.stderr}")
            
            # 下载日志文件
            log_cmd = f"scp -i {self.key_path} {self.username}@{self.server_ip}:{self.remote_dir}/*.log {local_results_dir}/"
            subprocess.run(log_cmd, shell=True, capture_output=True, text=True)
            
            # 下载其他可能的结果文件
            other_files = ["*.json", "*.csv", "*.png", "*.txt"]
            for pattern in other_files:
                download_cmd = f"scp -i {self.key_path} {self.username}@{self.server_ip}:{self.remote_dir}/{pattern} {local_results_dir}/ 2>/dev/null || true"
                subprocess.run(download_cmd, shell=True)
            
            logging.info(f"结果下载完成，保存在: {local_results_dir}")
            return local_results_dir
            
        except Exception as e:
            logging.error(f"下载结果时出错: {str(e)}")
            return None
    
    def cleanup_server(self):
        """清理云服务器上的测试文件"""
        logging.info("清理云服务器上的测试文件...")
        
        try:
            ssh_cmd = f"ssh -i {self.key_path} {self.username}@{self.server_ip}"
            cleanup_cmd = f"{ssh_cmd} 'rm -rf {self.remote_dir}'"
            subprocess.run(cleanup_cmd, shell=True, check=True)
            
            logging.info("云服务器清理完成")
            return True
            
        except Exception as e:
            logging.error(f"清理云服务器时出错: {str(e)}")
            return False
    
    def run_full_test_pipeline(self, test_config=None, cleanup=True, skip_install=True):
        """运行完整的测试流水线"""
        logging.info("开始完整的云服务器优化器测试流水线")

        try:
            # 1. 检查本地文件
            if not self.check_local_files():
                return False

            # 2. 上传文件
            if not self.upload_files_to_server():
                return False

            # 3. 安装依赖（可选跳过）
            if not skip_install:
                if not self.install_dependencies():
                    return False
            else:
                logging.info("跳过依赖安装（环境已准备好）")

            # 4. 运行测试
            if not self.run_optimizer_test(test_config):
                return False

            # 5. 下载结果
            results_dir = self.download_results()
            if not results_dir:
                return False

            # 6. 清理服务器（可选）
            if cleanup:
                self.cleanup_server()

            logging.info(f"完整测试流水线完成，结果保存在: {results_dir}")
            return results_dir

        except Exception as e:
            logging.error(f"测试流水线执行失败: {str(e)}")
            return False


def create_test_config():
    """创建测试配置"""
    return {
        'data_config': {
            'n_samples': 5000,      # 云服务器上使用较小的数据集
            'n_features': 20,
            'sequence_length': 60
        },
        'test_config': {
            'learning_rate': 1e-4,
            'batch_size': 32,       # 较小的批次大小
            'epochs': 30,           # 较少的训练轮数以节省时间
            'loss': 'mse',
            'metrics': ['mae', 'mse']
        },
        'optimizer_list': ['adam', 'adamw', 'lion', 'ademamix', 'fractional']
    }


def main():
    """主函数"""
    print("="*60)
    print("云服务器优化器测试系统")
    print("="*60)
    
    # 创建测试实例
    cloud_test = CloudServerTest()
    
    # 创建测试配置
    test_config = create_test_config()
    
    print(f"测试配置:")
    print(f"  数据样本数: {test_config['data_config']['n_samples']}")
    print(f"  特征数量: {test_config['data_config']['n_features']}")
    print(f"  训练轮数: {test_config['test_config']['epochs']}")
    print(f"  优化器列表: {test_config['optimizer_list']}")
    print()
    
    # 询问用户是否继续
    response = input("是否开始云服务器测试？(y/n): ").lower().strip()
    if response != 'y':
        print("测试已取消")
        return
    
    # 运行完整测试流水线（跳过依赖安装）
    start_time = time.time()
    results_dir = cloud_test.run_full_test_pipeline(test_config, cleanup=True, skip_install=True)
    end_time = time.time()
    
    if results_dir:
        print("\n" + "="*60)
        print("测试完成！")
        print(f"总耗时: {(end_time - start_time)/60:.2f} 分钟")
        print(f"结果保存在: {results_dir}")
        print("="*60)
        
        # 尝试显示简要结果
        try:
            result_files = list(Path(results_dir).glob("**/financial_optimizer_results.json"))
            if result_files:
                with open(result_files[0], 'r', encoding='utf-8') as f:
                    results = json.load(f)
                
                if 'financial_analysis' in results and 'recommendations' in results['financial_analysis']:
                    recommendations = results['financial_analysis']['recommendations']
                    print(f"\n推荐结果:")
                    print(f"风险调整后最佳优化器: {recommendations.get('best_risk_adjusted', 'N/A')}")
                    print(f"效率最佳优化器: {recommendations.get('best_efficiency', 'N/A')}")
        except Exception as e:
            logging.warning(f"显示结果摘要失败: {str(e)}")
    else:
        print("\n测试失败，请检查日志文件")


if __name__ == "__main__":
    main()
