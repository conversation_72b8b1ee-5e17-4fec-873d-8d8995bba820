#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复测试脚本 - 验证日志中发现的4个问题的修复方案
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_problem_1_limit_step_removal():
    """测试问题1: 移除limit_step接口调用"""
    print("🔧 测试1: 验证limit_step接口移除")
    print("-" * 40)
    
    # 模拟原有的获取连板天梯数据函数
    def get_limit_step_data_old(start_date, end_date):
        """原有的获取连板天梯数据（会失败）"""
        # 这里会调用limit_step接口，导致权限错误
        raise Exception("❌ limit_step 权限不足 (需要更高积分等级)")
    
    # 新的替代方案
    def get_limit_step_data_new(limit_data):
        """基于涨跌停数据构建连板天梯替代方案"""
        if limit_data.empty:
            logging.warning("⚠️ 涨跌停数据为空，无法构建连板天梯")
            return pd.DataFrame()
        
        # 筛选涨停数据
        limit_up_data = limit_data[limit_data['limit'] == 'U'].copy()
        
        if limit_up_data.empty:
            logging.warning("⚠️ 无涨停数据")
            return pd.DataFrame()
        
        # 按连板数统计
        step_summary = limit_up_data.groupby('limit_times').agg({
            'ts_code': 'count',
            'name': lambda x: ', '.join(x.head(3))  # 显示前3个股票名称
        }).rename(columns={'ts_code': 'count', 'name': 'sample_stocks'})
        
        return step_summary
    
    # 测试原有方案（会失败）
    try:
        old_data = get_limit_step_data_old('20241201', '20241201')
        print("❌ 原有方案应该失败但没有失败")
    except Exception as e:
        print(f"✅ 原有方案正确失败: {e}")
    
    # 测试新方案
    # 模拟涨跌停数据
    mock_limit_data = pd.DataFrame({
        'ts_code': ['000001.SZ', '000002.SZ', '000003.SZ', '000004.SZ'],
        'name': ['平安银行', '万科A', '国农科技', '国华网安'],
        'limit': ['U', 'U', 'U', 'D'],
        'limit_times': [1, 2, 3, 1]
    })
    
    new_data = get_limit_step_data_new(mock_limit_data)
    if not new_data.empty:
        print("✅ 新方案成功构建连板天梯数据:")
        print(new_data)
    else:
        print("❌ 新方案失败")
    
    return True

def test_problem_2_astype_error():
    """测试问题2: 'list' object has no attribute 'astype' 错误"""
    print("\n🔧 测试2: 验证astype错误修复")
    print("-" * 40)
    
    def check_classification_distribution_old(values, dataset_name):
        """原有的有问题的函数"""
        # 问题：values可能是list，没有astype方法
        value_counts = np.bincount(values.astype(int))  # 这里会出错
        return value_counts
    
    def check_classification_distribution_new(values, dataset_name):
        """修复后的函数"""
        # 确保values是numpy数组
        if isinstance(values, list):
            values = np.array(values)
        elif hasattr(values, 'values'):  # pandas Series
            values = values.values
        
        # 确保是整数类型
        values = values.astype(int)
        value_counts = np.bincount(values)
        
        print(f"✅ {dataset_name} 分布检查成功: {dict(enumerate(value_counts))}")
        return value_counts
    
    # 测试数据
    test_cases = [
        ([0, 1, 0, 1, 1], "list类型"),
        (np.array([0, 1, 0, 1, 1]), "numpy数组"),
        (pd.Series([0, 1, 0, 1, 1]), "pandas Series")
    ]
    
    for values, case_name in test_cases:
        print(f"测试 {case_name}:")
        
        # 测试原有函数（会失败）
        try:
            old_result = check_classification_distribution_old(values, case_name)
            print(f"  原有函数成功（意外）")
        except Exception as e:
            print(f"  原有函数失败（预期）: {type(e).__name__}")
        
        # 测试新函数
        try:
            new_result = check_classification_distribution_new(values, case_name)
            print(f"  新函数成功")
        except Exception as e:
            print(f"  新函数失败: {e}")
    
    return True

def test_problem_3_sequence_data_generation():
    """测试问题3: 首板策略序列数据生成问题"""
    print("\n🔧 测试3: 验证序列数据生成修复")
    print("-" * 40)
    
    def diagnose_sequence_data_problem(df, sequence_length=21):
        """诊断序列数据生成问题"""
        print(f"📊 数据诊断:")
        print(f"  总行数: {len(df)}")
        print(f"  序列长度要求: {sequence_length}")
        
        if 'ts_code' in df.columns:
            stock_counts = df['ts_code'].value_counts()
            print(f"  股票数量: {len(stock_counts)}")
            print(f"  每股数据量统计: min={stock_counts.min()}, max={stock_counts.max()}, mean={stock_counts.mean():.1f}")
            
            # 检查有足够数据的股票
            sufficient_data_stocks = stock_counts[stock_counts >= sequence_length]
            print(f"  数据量>={sequence_length}的股票数: {len(sufficient_data_stocks)}/{len(stock_counts)}")
            
            if len(sufficient_data_stocks) == 0:
                print("❌ 没有股票有足够的数据生成序列")
                return False
            else:
                print(f"✅ 有{len(sufficient_data_stocks)}只股票可以生成序列")
                return True
        else:
            print("❌ 数据中没有ts_code列")
            return False
    
    # 模拟首板策略数据（数据不足的情况）
    insufficient_data = pd.DataFrame({
        'ts_code': ['000001.SZ'] * 5 + ['000002.SZ'] * 3 + ['000003.SZ'] * 2,
        'trade_date': ['20241201', '20241202', '20241203', '20241204', '20241205'] * 2,
        'close': [10.0, 10.1, 10.2, 10.3, 10.4] * 2
    })
    
    print("测试数据不足的情况:")
    result1 = diagnose_sequence_data_problem(insufficient_data, 21)
    
    # 模拟有足够数据的情况
    sufficient_data = pd.DataFrame({
        'ts_code': ['000001.SZ'] * 25 + ['000002.SZ'] * 30,
        'trade_date': [f'2024{i:04d}' for i in range(1201, 1226)] + [f'2024{i:04d}' for i in range(1201, 1231)],
        'close': np.random.random(55) * 10 + 10
    })
    
    print("\n测试数据充足的情况:")
    result2 = diagnose_sequence_data_problem(sufficient_data, 21)
    
    return result2

def test_problem_4_feature_calculation():
    """测试问题4: 连板特征计算修复"""
    print("\n🔧 测试4: 验证连板特征计算修复")
    print("-" * 40)
    
    def calculate_limit_features_old(df):
        """原有的连板特征计算（依赖limit_step数据）"""
        # 原有方法依赖连板天梯数据，现在无法获取
        if 'limit_step_data' not in df.columns:
            raise Exception("❌ 缺少连板天梯数据，无法计算连板特征")
    
    def calculate_limit_features_new(df):
        """新的连板特征计算（基于涨跌停数据）"""
        if df.empty:
            return df
        
        # 基于涨跌停数据计算连板特征
        df = df.copy()
        
        # 连板次数特征（如果有limit_times列）
        if 'limit_times' in df.columns:
            df['is_limit_up'] = (df.get('limit', '') == 'U')
            df['is_continuous_limit'] = df['limit_times'] > 1
            df['limit_strength'] = df['limit_times'].fillna(0)
        else:
            # 如果没有limit_times，基于涨跌幅计算
            if 'pct_chg' in df.columns:
                df['is_limit_up'] = df['pct_chg'] >= 9.8
                df['is_continuous_limit'] = False  # 默认值
                df['limit_strength'] = df['pct_chg'].apply(lambda x: 1 if x >= 9.8 else 0)
            else:
                # 使用默认值
                df['is_limit_up'] = False
                df['is_continuous_limit'] = False
                df['limit_strength'] = 0
        
        print(f"✅ 成功计算连板特征，涨停股票数: {df['is_limit_up'].sum()}")
        return df
    
    # 测试数据
    test_data = pd.DataFrame({
        'ts_code': ['000001.SZ', '000002.SZ', '000003.SZ'],
        'pct_chg': [10.0, 5.0, -2.0],
        'limit': ['U', '', ''],
        'limit_times': [2, 0, 0]
    })
    
    # 测试原有方法
    try:
        old_result = calculate_limit_features_old(test_data)
        print("❌ 原有方法应该失败但没有失败")
    except Exception as e:
        print(f"✅ 原有方法正确失败: {e}")
    
    # 测试新方法
    try:
        new_result = calculate_limit_features_new(test_data)
        print("✅ 新方法成功")
        print(f"  结果列: {list(new_result.columns)}")
        print(f"  涨停标记: {new_result['is_limit_up'].tolist()}")
    except Exception as e:
        print(f"❌ 新方法失败: {e}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始修复验证测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    results = []
    
    # 测试所有问题的修复
    results.append(test_problem_1_limit_step_removal())
    results.append(test_problem_2_astype_error())
    results.append(test_problem_3_sequence_data_generation())
    results.append(test_problem_4_feature_calculation())
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试总结")
    print("=" * 50)
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"✅ 成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有测试通过！可以开始修复主程序")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    print("\n📋 修复建议:")
    print("1. 移除所有limit_step接口调用")
    print("2. 修复astype错误的类型检查")
    print("3. 改进序列数据生成的数据量检查")
    print("4. 使用涨跌停数据替代连板天梯数据计算特征")

if __name__ == "__main__":
    main()
