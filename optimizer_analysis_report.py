#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化器测试结果分析报告
综合本地和云服务器测试结果，选择最适合金融时间序列预测的优化器
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime

class OptimizerAnalysisReport:
    """优化器分析报告生成器"""
    
    def __init__(self):
        self.local_results = None
        self.cloud_results = None
        self.analysis_results = {}
    
    def load_test_results(self):
        """加载测试结果"""
        # 本地测试结果
        local_data = {
            'adam': {'final_val_loss': 0.072546, 'convergence_speed': 68, 'training_time': 73.24, 'stability': 0.000731, 'memory_mb': 91.82},
            'adamw': {'final_val_loss': 0.070903, 'convergence_speed': 70, 'training_time': 73.28, 'stability': 0.000792, 'memory_mb': 48.74},
            'lion': {'final_val_loss': 0.071887, 'convergence_speed': 67, 'training_time': 72.01, 'stability': 0.000648, 'memory_mb': 36.39},
            'ademamix': {'final_val_loss': 0.068368, 'convergence_speed': 77, 'training_time': 69.38, 'stability': 0.001030, 'memory_mb': 43.37},
            'fractional': {'final_val_loss': 0.072732, 'convergence_speed': 73, 'training_time': 69.89, 'stability': 0.000792, 'memory_mb': 36.14}
        }
        
        # 云服务器测试结果
        cloud_data = {
            'adam': {'final_val_loss': 0.067592, 'convergence_speed': 71, 'training_time': 38.37, 'stability': 0.000699, 'memory_mb': 251.06},
            'adamw': {'final_val_loss': 0.071302, 'convergence_speed': 64, 'training_time': 40.16, 'stability': 0.000524, 'memory_mb': 65.96},
            'lion': {'final_val_loss': 0.068327, 'convergence_speed': 80, 'training_time': 40.48, 'stability': 0.001282, 'memory_mb': 36.43},
            'ademamix': {'final_val_loss': 0.070995, 'convergence_speed': 69, 'training_time': 40.44, 'stability': 0.000475, 'memory_mb': 35.96},
            'fractional': {'final_val_loss': 0.067087, 'convergence_speed': 80, 'training_time': 40.22, 'stability': 0.000921, 'memory_mb': 34.91}
        }
        
        self.local_results = local_data
        self.cloud_results = cloud_data
        
        print("✓ 测试结果加载完成")
        print(f"  本地测试: {len(local_data)} 个优化器")
        print(f"  云服务器测试: {len(cloud_data)} 个优化器")
    
    def calculate_comprehensive_scores(self):
        """计算综合评分"""
        print("\n📊 计算综合评分...")
        
        # 权重设置（基于金融应用的重要性）
        weights = {
            'performance': 0.35,    # 性能（验证损失）- 最重要
            'stability': 0.25,      # 稳定性 - 金融应用关键
            'efficiency': 0.20,     # 效率（训练时间）
            'memory': 0.15,         # 内存使用
            'convergence': 0.05     # 收敛速度
        }
        
        # 合并本地和云服务器结果（取平均值）
        combined_results = {}
        for optimizer in self.local_results.keys():
            local = self.local_results[optimizer]
            cloud = self.cloud_results[optimizer]
            
            combined_results[optimizer] = {
                'final_val_loss': (local['final_val_loss'] + cloud['final_val_loss']) / 2,
                'convergence_speed': (local['convergence_speed'] + cloud['convergence_speed']) / 2,
                'training_time': (local['training_time'] + cloud['training_time']) / 2,
                'stability': (local['stability'] + cloud['stability']) / 2,
                'memory_mb': (local['memory_mb'] + cloud['memory_mb']) / 2
            }
        
        # 标准化指标（0-1范围）
        optimizers = list(combined_results.keys())
        
        # 提取各项指标
        val_losses = [combined_results[opt]['final_val_loss'] for opt in optimizers]
        stabilities = [combined_results[opt]['stability'] for opt in optimizers]
        training_times = [combined_results[opt]['training_time'] for opt in optimizers]
        memory_usages = [combined_results[opt]['memory_mb'] for opt in optimizers]
        convergence_speeds = [combined_results[opt]['convergence_speed'] for opt in optimizers]
        
        # 标准化（越小越好的指标需要反转）
        def normalize_reverse(values):
            """标准化并反转（越小越好）"""
            min_val, max_val = min(values), max(values)
            if max_val == min_val:
                return [1.0] * len(values)
            return [(max_val - v) / (max_val - min_val) for v in values]
        
        def normalize_forward(values):
            """标准化（越大越好）"""
            min_val, max_val = min(values), max(values)
            if max_val == min_val:
                return [1.0] * len(values)
            return [(v - min_val) / (max_val - min_val) for v in values]
        
        # 计算标准化分数
        performance_scores = normalize_reverse(val_losses)
        stability_scores = normalize_reverse(stabilities)  # 稳定性：波动越小越好
        efficiency_scores = normalize_reverse(training_times)
        memory_scores = normalize_reverse(memory_usages)
        convergence_scores = normalize_reverse(convergence_speeds)  # 收敛速度：epoch越少越好
        
        # 计算综合得分
        comprehensive_scores = {}
        for i, optimizer in enumerate(optimizers):
            score = (
                weights['performance'] * performance_scores[i] +
                weights['stability'] * stability_scores[i] +
                weights['efficiency'] * efficiency_scores[i] +
                weights['memory'] * memory_scores[i] +
                weights['convergence'] * convergence_scores[i]
            )
            comprehensive_scores[optimizer] = {
                'total_score': score,
                'performance_score': performance_scores[i],
                'stability_score': stability_scores[i],
                'efficiency_score': efficiency_scores[i],
                'memory_score': memory_scores[i],
                'convergence_score': convergence_scores[i],
                'combined_metrics': combined_results[optimizer]
            }
        
        self.analysis_results['comprehensive_scores'] = comprehensive_scores
        self.analysis_results['weights'] = weights
        
        # 排序
        sorted_optimizers = sorted(comprehensive_scores.items(), 
                                 key=lambda x: x[1]['total_score'], reverse=True)
        
        print("综合评分结果（按总分排序）:")
        print("-" * 60)
        for i, (optimizer, scores) in enumerate(sorted_optimizers):
            print(f"{i+1}. {optimizer:12} - 总分: {scores['total_score']:.4f}")
            print(f"   性能: {scores['performance_score']:.3f} | "
                  f"稳定性: {scores['stability_score']:.3f} | "
                  f"效率: {scores['efficiency_score']:.3f} | "
                  f"内存: {scores['memory_score']:.3f}")
        
        return sorted_optimizers
    
    def financial_specific_analysis(self):
        """金融特定分析"""
        print("\n💰 金融特定分析...")
        
        scores = self.analysis_results['comprehensive_scores']
        
        # 金融应用的特殊考虑
        financial_analysis = {}
        
        for optimizer, data in scores.items():
            metrics = data['combined_metrics']
            
            # 计算风险调整后的性能（类似夏普比率）
            risk_adjusted_performance = -metrics['final_val_loss'] / (metrics['stability'] + 1e-8)
            
            # 计算效率比（性能/时间）
            efficiency_ratio = -metrics['final_val_loss'] / (metrics['training_time'] + 1e-8)
            
            # 计算内存效率（性能/内存）
            memory_efficiency = -metrics['final_val_loss'] / (metrics['memory_mb'] + 1e-8)
            
            # 金融稳定性评分（考虑低波动的重要性）
            stability_score = 1.0 / (metrics['stability'] + 1e-8)
            
            financial_analysis[optimizer] = {
                'risk_adjusted_performance': risk_adjusted_performance,
                'efficiency_ratio': efficiency_ratio,
                'memory_efficiency': memory_efficiency,
                'stability_score': stability_score,
                'final_val_loss': metrics['final_val_loss']
            }
        
        # 排序各项金融指标
        best_risk_adjusted = max(financial_analysis.items(), 
                               key=lambda x: x[1]['risk_adjusted_performance'])
        best_efficiency = max(financial_analysis.items(), 
                            key=lambda x: x[1]['efficiency_ratio'])
        best_memory_efficiency = max(financial_analysis.items(), 
                                   key=lambda x: x[1]['memory_efficiency'])
        best_stability = max(financial_analysis.items(), 
                           key=lambda x: x[1]['stability_score'])
        best_performance = min(financial_analysis.items(), 
                             key=lambda x: x[1]['final_val_loss'])
        
        print("金融特定指标最佳优化器:")
        print("-" * 40)
        print(f"风险调整后性能最佳: {best_risk_adjusted[0]}")
        print(f"效率最佳: {best_efficiency[0]}")
        print(f"内存效率最佳: {best_memory_efficiency[0]}")
        print(f"稳定性最佳: {best_stability[0]}")
        print(f"绝对性能最佳: {best_performance[0]}")
        
        self.analysis_results['financial_analysis'] = financial_analysis
        
        return {
            'best_risk_adjusted': best_risk_adjusted[0],
            'best_efficiency': best_efficiency[0],
            'best_memory_efficiency': best_memory_efficiency[0],
            'best_stability': best_stability[0],
            'best_performance': best_performance[0]
        }
    
    def generate_final_recommendation(self):
        """生成最终推荐"""
        print("\n🎯 生成最终推荐...")
        
        comprehensive_ranking = sorted(self.analysis_results['comprehensive_scores'].items(), 
                                     key=lambda x: x[1]['total_score'], reverse=True)
        
        financial_best = self.financial_specific_analysis()
        
        # 综合考虑选择最佳优化器
        top_3 = [item[0] for item in comprehensive_ranking[:3]]
        
        print(f"\n综合排名前3: {top_3}")
        print(f"金融指标最佳: {financial_best}")
        
        # 最终推荐逻辑
        if comprehensive_ranking[0][0] in [financial_best['best_risk_adjusted'], 
                                          financial_best['best_performance']]:
            final_recommendation = comprehensive_ranking[0][0]
            reason = "综合评分最高且在关键金融指标中表现优秀"
        else:
            # 如果综合第一不是金融最佳，选择平衡的选项
            final_recommendation = financial_best['best_risk_adjusted']
            reason = "风险调整后性能最佳，更适合金融应用"
        
        recommendation = {
            'primary_recommendation': final_recommendation,
            'reason': reason,
            'comprehensive_ranking': top_3,
            'financial_best': financial_best,
            'detailed_scores': self.analysis_results['comprehensive_scores'][final_recommendation]
        }
        
        self.analysis_results['final_recommendation'] = recommendation
        
        print(f"\n🏆 最终推荐: {final_recommendation}")
        print(f"推荐理由: {reason}")
        
        return recommendation
    
    def save_analysis_report(self, filename=None):
        """保存分析报告"""
        if filename is None:
            filename = f"optimizer_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 分析报告已保存: {filename}")
        return filename
    
    def print_summary_report(self):
        """打印摘要报告"""
        print("\n" + "="*80)
        print("优化器测试结果分析摘要报告")
        print("="*80)
        
        recommendation = self.analysis_results['final_recommendation']
        
        print(f"\n🏆 最终推荐优化器: {recommendation['primary_recommendation'].upper()}")
        print(f"推荐理由: {recommendation['reason']}")
        
        print(f"\n📊 综合排名前3:")
        for i, opt in enumerate(recommendation['comprehensive_ranking']):
            score = self.analysis_results['comprehensive_scores'][opt]['total_score']
            print(f"  {i+1}. {opt} (综合得分: {score:.4f})")
        
        print(f"\n💰 金融特定指标最佳:")
        financial_best = recommendation['financial_best']
        for metric, optimizer in financial_best.items():
            print(f"  {metric}: {optimizer}")
        
        # 推荐优化器的详细信息
        best_opt = recommendation['primary_recommendation']
        best_scores = recommendation['detailed_scores']
        best_metrics = best_scores['combined_metrics']
        
        print(f"\n📈 推荐优化器 ({best_opt}) 详细性能:")
        print(f"  验证损失: {best_metrics['final_val_loss']:.6f}")
        print(f"  收敛速度: {best_metrics['convergence_speed']:.1f} epochs")
        print(f"  训练时间: {best_metrics['training_time']:.2f} 秒")
        print(f"  稳定性: {best_metrics['stability']:.6f}")
        print(f"  内存使用: {best_metrics['memory_mb']:.2f} MB")
        print(f"  综合得分: {best_scores['total_score']:.4f}")
        
        print("\n" + "="*80)


def main():
    """主函数"""
    print("优化器测试结果分析")
    print("="*50)
    
    # 创建分析器
    analyzer = OptimizerAnalysisReport()
    
    # 加载测试结果
    analyzer.load_test_results()
    
    # 计算综合评分
    analyzer.calculate_comprehensive_scores()
    
    # 生成最终推荐
    analyzer.generate_final_recommendation()
    
    # 保存分析报告
    analyzer.save_analysis_report()
    
    # 打印摘要报告
    analyzer.print_summary_report()


if __name__ == "__main__":
    main()
