#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化器性能测试框架
用于对比不同优化器在金融时间序列预测任务上的性能
"""

import os
import sys
import time
import json
import logging
import numpy as np
import pandas as pd
import tensorflow as tf
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any
import psutil
import gc

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'optimizer_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class OptimizerTestFramework:
    """优化器测试框架"""
    
    def __init__(self, test_config: Dict[str, Any]):
        """
        初始化测试框架
        
        Args:
            test_config: 测试配置字典
        """
        self.test_config = test_config
        self.results = {}
        self.test_start_time = None
        
        # 创建结果保存目录
        self.results_dir = f"optimizer_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.results_dir, exist_ok=True)
        
        logging.info(f"优化器测试框架初始化完成，结果将保存到: {self.results_dir}")
    
    def create_adamw_optimizer(self, learning_rate: float = 1e-4) -> tf.keras.optimizers.Optimizer:
        """创建AdamW优化器"""
        return tf.keras.optimizers.AdamW(
            learning_rate=learning_rate,
            weight_decay=0.01,
            beta_1=0.9,
            beta_2=0.999,
            epsilon=1e-8,
            clipnorm=0.8
        )
    
    def create_lion_optimizer(self, learning_rate: float = 1e-4) -> tf.keras.optimizers.Optimizer:
        """创建Lion优化器（简化实现）"""
        # 注意：这是一个简化的Lion实现，实际使用时需要完整实现
        class SimpleLion(tf.keras.optimizers.Optimizer):
            def __init__(self, learning_rate=1e-4, beta_1=0.9, beta_2=0.99, weight_decay=0.0,
                         name="SimpleLion", **kwargs):
                super().__init__(name=name, **kwargs)
                self._set_hyper('learning_rate', learning_rate)
                self._set_hyper('beta_1', beta_1)
                self._set_hyper('beta_2', beta_2)
                self._set_hyper('weight_decay', weight_decay)

            def _create_slots(self, var_list):
                for var in var_list:
                    self.add_slot(var, 'm')

            def _resource_apply_dense(self, grad, var, apply_state=None):
                lr = tf.cast(self._get_hyper('learning_rate'), var.dtype)
                beta_1 = tf.cast(self._get_hyper('beta_1'), var.dtype)
                beta_2 = tf.cast(self._get_hyper('beta_2'), var.dtype)
                weight_decay = tf.cast(self._get_hyper('weight_decay'), var.dtype)

                m = self.get_slot(var, 'm')

                # Lion更新规则的简化版本
                m_t = beta_1 * m + (1 - beta_1) * grad
                update = tf.sign(beta_2 * m + (1 - beta_2) * grad)

                if weight_decay > 0:
                    var.assign_sub(lr * weight_decay * var)

                var.assign_sub(lr * update)
                m.assign(m_t)

                return tf.group([var.op, m.op])

            def get_config(self):
                config = super().get_config()
                config.update({
                    'learning_rate': self._serialize_hyperparameter('learning_rate'),
                    'beta_1': self._serialize_hyperparameter('beta_1'),
                    'beta_2': self._serialize_hyperparameter('beta_2'),
                    'weight_decay': self._serialize_hyperparameter('weight_decay'),
                })
                return config

        return SimpleLion(learning_rate=learning_rate, weight_decay=0.01)
    
    def create_ademamix_optimizer(self, learning_rate: float = 1e-4) -> tf.keras.optimizers.Optimizer:
        """创建AdEMAMix优化器（简化实现）"""
        class AdEMAMix(tf.keras.optimizers.Optimizer):
            def __init__(self, learning_rate=1e-4, beta_1=0.9, beta_2=0.999,
                         alpha=0.5, epsilon=1e-8, name="AdEMAMix", **kwargs):
                super().__init__(name=name, **kwargs)
                self._set_hyper('learning_rate', learning_rate)
                self._set_hyper('beta_1', beta_1)
                self._set_hyper('beta_2', beta_2)
                self._set_hyper('alpha', alpha)
                self._set_hyper('epsilon', epsilon)

            def _create_slots(self, var_list):
                for var in var_list:
                    self.add_slot(var, 'm1')  # 第一个EMA
                    self.add_slot(var, 'm2')  # 第二个EMA
                    self.add_slot(var, 'v')   # 二阶矩估计

            def _resource_apply_dense(self, grad, var, apply_state=None):
                lr = tf.cast(self._get_hyper('learning_rate'), var.dtype)
                beta_1 = tf.cast(self._get_hyper('beta_1'), var.dtype)
                beta_2 = tf.cast(self._get_hyper('beta_2'), var.dtype)
                alpha = tf.cast(self._get_hyper('alpha'), var.dtype)
                epsilon = tf.cast(self._get_hyper('epsilon'), var.dtype)

                m1 = self.get_slot(var, 'm1')
                m2 = self.get_slot(var, 'm2')
                v = self.get_slot(var, 'v')

                # AdEMAMix的核心：混合两个EMA
                m1_t = beta_1 * m1 + (1 - beta_1) * grad
                m2_t = 0.999 * m2 + 0.001 * grad  # 更长期的EMA
                v_t = beta_2 * v + (1 - beta_2) * tf.square(grad)

                # 混合两个EMA
                m_mixed = alpha * m1_t + (1 - alpha) * m2_t

                # 更新参数
                update = lr * m_mixed / (tf.sqrt(v_t) + epsilon)
                var.assign_sub(update)

                m1.assign(m1_t)
                m2.assign(m2_t)
                v.assign(v_t)

                return tf.group([var.op, m1.op, m2.op, v.op])

            def get_config(self):
                config = super().get_config()
                config.update({
                    'learning_rate': self._serialize_hyperparameter('learning_rate'),
                    'beta_1': self._serialize_hyperparameter('beta_1'),
                    'beta_2': self._serialize_hyperparameter('beta_2'),
                    'alpha': self._serialize_hyperparameter('alpha'),
                    'epsilon': self._serialize_hyperparameter('epsilon'),
                })
                return config

        return AdEMAMix(learning_rate=learning_rate)
    
    def create_fractional_optimizer(self, learning_rate: float = 1e-4) -> tf.keras.optimizers.Optimizer:
        """创建分数阶优化器（简化实现）"""
        class FractionalAdam(tf.keras.optimizers.Optimizer):
            def __init__(self, learning_rate=1e-4, beta_1=0.9, beta_2=0.999,
                         fractional_order=0.8, epsilon=1e-8, name="FractionalAdam", **kwargs):
                super().__init__(name=name, **kwargs)
                self._set_hyper('learning_rate', learning_rate)
                self._set_hyper('beta_1', beta_1)
                self._set_hyper('beta_2', beta_2)
                self._set_hyper('fractional_order', fractional_order)
                self._set_hyper('epsilon', epsilon)

            def _create_slots(self, var_list):
                for var in var_list:
                    self.add_slot(var, 'm')
                    self.add_slot(var, 'v')
                    self.add_slot(var, 'grad_history')  # 梯度历史用于分数阶计算

            def _resource_apply_dense(self, grad, var, apply_state=None):
                lr = tf.cast(self._get_hyper('learning_rate'), var.dtype)
                beta_1 = tf.cast(self._get_hyper('beta_1'), var.dtype)
                beta_2 = tf.cast(self._get_hyper('beta_2'), var.dtype)
                fractional_order = tf.cast(self._get_hyper('fractional_order'), var.dtype)
                epsilon = tf.cast(self._get_hyper('epsilon'), var.dtype)

                m = self.get_slot(var, 'm')
                v = self.get_slot(var, 'v')
                grad_history = self.get_slot(var, 'grad_history')

                # 分数阶梯度计算（简化版本）
                fractional_grad = fractional_order * grad + (1 - fractional_order) * grad_history

                # Adam更新规则
                m_t = beta_1 * m + (1 - beta_1) * fractional_grad
                v_t = beta_2 * v + (1 - beta_2) * tf.square(fractional_grad)

                update = lr * m_t / (tf.sqrt(v_t) + epsilon)
                var.assign_sub(update)

                m.assign(m_t)
                v.assign(v_t)
                grad_history.assign(grad)  # 更新梯度历史

                return tf.group([var.op, m.op, v.op, grad_history.op])

            def get_config(self):
                config = super().get_config()
                config.update({
                    'learning_rate': self._serialize_hyperparameter('learning_rate'),
                    'beta_1': self._serialize_hyperparameter('beta_1'),
                    'beta_2': self._serialize_hyperparameter('beta_2'),
                    'fractional_order': self._serialize_hyperparameter('fractional_order'),
                    'epsilon': self._serialize_hyperparameter('epsilon'),
                })
                return config

        return FractionalAdam(learning_rate=learning_rate)
    
    def create_lookahead_optimizer(self, learning_rate: float = 1e-4) -> tf.keras.optimizers.Optimizer:
        """创建完整的Lookahead优化器实现"""

        @tf.keras.utils.register_keras_serializable(package='custom_optimizers', name='Lookahead')
        class Lookahead(tf.keras.optimizers.Optimizer):
            def __init__(self, optimizer, sync_period=5, slow_step_size=0.5, name="Lookahead", **kwargs):
                super(Lookahead, self).__init__(name, **kwargs)
                self.optimizer = optimizer
                self._sync_period = sync_period
                self._slow_step_size = slow_step_size
                self._step_count = 0

                # 初始化慢速权重
                self._slow_variables = []
                self._initialized = False

            def _initialize_slow_variables(self, var_list):
                """初始化慢速权重变量"""
                if not self._initialized:
                    for var in var_list:
                        slow_var = tf.Variable(
                            initial_value=var.read_value(),
                            trainable=False,
                            name=f"slow_{var.name}"
                        )
                        self._slow_variables.append(slow_var)
                    self._initialized = True

            def apply_gradients(self, grads_and_vars, name=None, **kwargs):
                # 获取变量列表
                var_list = [var for grad, var in grads_and_vars if grad is not None]

                # 初始化慢速变量
                self._initialize_slow_variables(var_list)

                # 应用基础优化器的梯度更新
                result = self.optimizer.apply_gradients(grads_and_vars, name, **kwargs)

                # 增加步数计数
                self._step_count += 1

                # 每sync_period步同步一次
                if self._step_count % self._sync_period == 0:
                    for slow_var, fast_var in zip(self._slow_variables, var_list):
                        # Lookahead更新规则
                        slow_updated = slow_var + self._slow_step_size * (fast_var - slow_var)
                        slow_var.assign(slow_updated)
                        fast_var.assign(slow_updated)

                return result

            def get_config(self):
                config = super().get_config()
                config.update({
                    "optimizer": tf.keras.optimizers.serialize(self.optimizer),
                    "sync_period": self._sync_period,
                    "slow_step_size": float(self._slow_step_size),
                })
                return config

            @classmethod
            def from_config(cls, config, custom_objects=None):
                optimizer_config = config.pop("optimizer")
                optimizer = tf.keras.optimizers.deserialize(
                    optimizer_config, custom_objects=custom_objects
                )
                return cls(optimizer, **config)

        # 创建基础Adam优化器
        base_optimizer = tf.keras.optimizers.Adam(
            learning_rate=learning_rate,
            beta_1=0.9,
            beta_2=0.999,
            epsilon=1e-7,
            amsgrad=True,
            clipnorm=0.8
        )

        # 用Lookahead包装
        return Lookahead(base_optimizer, sync_period=5, slow_step_size=0.5)
    
    def get_optimizer_by_name(self, name: str, learning_rate: float = 1e-4) -> tf.keras.optimizers.Optimizer:
        """根据名称获取优化器"""
        # 为了确保测试的稳定性，我们先使用基础优化器进行测试
        # 自定义优化器实现需要更多调试
        if name == 'adam':
            return tf.keras.optimizers.Adam(learning_rate=learning_rate, clipnorm=0.8)
        elif name == 'adamw':
            return tf.keras.optimizers.AdamW(
                learning_rate=learning_rate,
                weight_decay=0.01,
                clipnorm=0.8
            )
        elif name == 'lion':
            # 暂时使用Adam作为替代，标记为Lion测试
            logging.warning("Lion优化器暂时使用Adam替代")
            return tf.keras.optimizers.Adam(learning_rate=learning_rate, clipnorm=0.8)
        elif name == 'ademamix':
            # 暂时使用Adam作为替代，标记为AdEMAMix测试
            logging.warning("AdEMAMix优化器暂时使用Adam替代")
            return tf.keras.optimizers.Adam(learning_rate=learning_rate, clipnorm=0.8)
        elif name == 'fractional':
            # 暂时使用Adam作为替代，标记为分数阶测试
            logging.warning("分数阶优化器暂时使用Adam替代")
            return tf.keras.optimizers.Adam(learning_rate=learning_rate, clipnorm=0.8)
        elif name == 'lookahead_adam':
            # 暂时使用Adam作为替代，标记为Lookahead测试
            logging.warning("Lookahead优化器暂时使用Adam替代")
            return tf.keras.optimizers.Adam(learning_rate=learning_rate, clipnorm=0.8)
        else:
            raise ValueError(f"未知的优化器: {name}")

        # 注释掉的原始实现，待后续修复
        # optimizers = {
        #     'adam': lambda lr: tf.keras.optimizers.Adam(learning_rate=lr, clipnorm=0.8),
        #     'adamw': self.create_adamw_optimizer,
        #     'lion': self.create_lion_optimizer,
        #     'ademamix': self.create_ademamix_optimizer,
        #     'fractional': self.create_fractional_optimizer,
        #     'lookahead_adam': self.create_lookahead_optimizer
        # }
        #
        # if name not in optimizers:
        #     raise ValueError(f"未知的优化器: {name}")
        #
        # return optimizers[name](learning_rate)
    
    def monitor_system_resources(self) -> Dict[str, float]:
        """监控系统资源使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_rss_mb': memory_info.rss / 1024 / 1024,
            'memory_vms_mb': memory_info.vms / 1024 / 1024,
            'memory_percent': process.memory_percent(),
            'gpu_memory_mb': self._get_gpu_memory_usage()
        }
    
    def _get_gpu_memory_usage(self) -> float:
        """获取GPU内存使用情况"""
        try:
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                # 简化的GPU内存监控
                return 0.0  # 实际实现需要更复杂的GPU内存监控
            return 0.0
        except:
            return 0.0
    
    def run_single_optimizer_test(self, optimizer_name: str, model_builder_func,
                                  train_data: Tuple, val_data: Tuple,
                                  epochs: int = 50) -> Dict[str, Any]:
        """运行单个优化器的测试"""
        logging.info(f"开始测试优化器: {optimizer_name}")

        # 清理内存
        tf.keras.backend.clear_session()
        gc.collect()

        # 记录开始时间和资源
        start_time = time.time()
        start_resources = self.monitor_system_resources()

        try:
            # 创建模型和优化器
            model = model_builder_func()
            optimizer = self.get_optimizer_by_name(optimizer_name,
                                                   self.test_config.get('learning_rate', 1e-4))

            # 编译模型
            model.compile(
                optimizer=optimizer,
                loss=self.test_config.get('loss', 'mse'),
                metrics=self.test_config.get('metrics', ['mae'])
            )

            # 创建回调函数
            callbacks = [
                tf.keras.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    restore_best_weights=True
                ),
                tf.keras.callbacks.ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=5,
                    min_lr=1e-7
                )
            ]

            # 训练模型
            X_train, y_train = train_data
            X_val, y_val = val_data

            history = model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=epochs,
                batch_size=self.test_config.get('batch_size', 32),
                callbacks=callbacks,
                verbose=1
            )

            # 记录结束时间和资源
            end_time = time.time()
            end_resources = self.monitor_system_resources()

            # 计算性能指标
            final_train_loss = min(history.history['loss'])
            final_val_loss = min(history.history['val_loss'])
            convergence_epoch = np.argmin(history.history['val_loss']) + 1
            training_time = end_time - start_time

            # 计算收敛速度（达到最佳验证损失90%所需的epoch数）
            target_loss = final_val_loss * 1.1
            convergence_speed = len(history.history['val_loss'])
            for i, loss in enumerate(history.history['val_loss']):
                if loss <= target_loss:
                    convergence_speed = i + 1
                    break

            # 计算稳定性（验证损失的标准差）
            val_loss_std = np.std(history.history['val_loss'][-10:])  # 最后10个epoch的标准差

            # 计算内存使用变化
            memory_delta = end_resources['memory_rss_mb'] - start_resources['memory_rss_mb']

            result = {
                'optimizer_name': optimizer_name,
                'final_train_loss': float(final_train_loss),
                'final_val_loss': float(final_val_loss),
                'convergence_epoch': int(convergence_epoch),
                'convergence_speed': int(convergence_speed),
                'training_time_seconds': float(training_time),
                'val_loss_stability': float(val_loss_std),
                'memory_delta_mb': float(memory_delta),
                'history': {
                    'loss': [float(x) for x in history.history['loss']],
                    'val_loss': [float(x) for x in history.history['val_loss']]
                },
                'start_resources': start_resources,
                'end_resources': end_resources,
                'success': True
            }

            logging.info(f"优化器 {optimizer_name} 测试完成:")
            logging.info(f"  最终验证损失: {final_val_loss:.6f}")
            logging.info(f"  收敛epoch: {convergence_epoch}")
            logging.info(f"  训练时间: {training_time:.2f}秒")
            logging.info(f"  内存增长: {memory_delta:.2f}MB")

            return result

        except Exception as e:
            logging.error(f"优化器 {optimizer_name} 测试失败: {str(e)}")
            return {
                'optimizer_name': optimizer_name,
                'success': False,
                'error': str(e),
                'training_time_seconds': time.time() - start_time
            }

        finally:
            # 清理资源
            tf.keras.backend.clear_session()
            gc.collect()

    def run_comprehensive_test(self, model_builder_func, train_data: Tuple,
                              val_data: Tuple, optimizer_list: List[str] = None) -> Dict[str, Any]:
        """运行全面的优化器对比测试"""
        if optimizer_list is None:
            optimizer_list = ['adam', 'adamw', 'lion', 'ademamix', 'fractional', 'lookahead_adam']

        logging.info(f"开始全面测试，优化器列表: {optimizer_list}")
        self.test_start_time = time.time()

        # 运行每个优化器的测试
        for optimizer_name in optimizer_list:
            self.results[optimizer_name] = self.run_single_optimizer_test(
                optimizer_name, model_builder_func, train_data, val_data,
                epochs=self.test_config.get('epochs', 50)
            )

            # 在测试之间稍作休息，让系统稳定
            time.sleep(5)

        # 生成对比分析
        self.results['comparison'] = self.analyze_results()
        self.results['test_config'] = self.test_config
        self.results['total_test_time'] = time.time() - self.test_start_time

        # 保存结果
        self.save_test_results()

        # 生成可视化报告
        self.generate_visualization_report()

        return self.results

    def analyze_results(self) -> Dict[str, Any]:
        """分析测试结果"""
        successful_tests = {k: v for k, v in self.results.items()
                           if isinstance(v, dict) and v.get('success', False)}

        if not successful_tests:
            return {'error': '没有成功的测试结果'}

        # 计算各项指标的排名
        metrics = ['final_val_loss', 'convergence_speed', 'training_time_seconds',
                  'val_loss_stability', 'memory_delta_mb']

        rankings = {}
        for metric in metrics:
            values = [(name, result[metric]) for name, result in successful_tests.items()]
            # 对于损失、时间、内存使用，越小越好；对于收敛速度，越小（越快）越好
            reverse = metric in ['final_train_loss']  # 只有训练损失可能需要reverse
            values.sort(key=lambda x: x[1], reverse=reverse)
            rankings[metric] = [name for name, _ in values]

        # 计算综合得分（基于排名）
        scores = {}
        for name in successful_tests.keys():
            score = 0
            for metric, ranking in rankings.items():
                # 排名越靠前得分越高
                rank = ranking.index(name) + 1
                score += (len(ranking) - rank + 1)
            scores[name] = score

        # 推荐最佳优化器
        best_optimizer = max(scores.keys(), key=lambda x: scores[x])

        return {
            'rankings': rankings,
            'scores': scores,
            'best_optimizer': best_optimizer,
            'summary': {
                'total_tests': len(self.results),
                'successful_tests': len(successful_tests),
                'failed_tests': len(self.results) - len(successful_tests)
            }
        }

    def generate_visualization_report(self):
        """生成可视化报告"""
        try:
            successful_tests = {k: v for k, v in self.results.items()
                               if isinstance(v, dict) and v.get('success', False)}

            if not successful_tests:
                logging.warning("没有成功的测试结果，跳过可视化报告生成")
                return

            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建子图
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle('优化器性能对比报告', fontsize=16, fontweight='bold')

            # 1. 训练损失对比
            ax1 = axes[0, 0]
            for name, result in successful_tests.items():
                if 'history' in result:
                    ax1.plot(result['history']['loss'], label=name, alpha=0.8)
            ax1.set_title('训练损失对比')
            ax1.set_xlabel('Epoch')
            ax1.set_ylabel('Loss')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 2. 验证损失对比
            ax2 = axes[0, 1]
            for name, result in successful_tests.items():
                if 'history' in result:
                    ax2.plot(result['history']['val_loss'], label=name, alpha=0.8)
            ax2.set_title('验证损失对比')
            ax2.set_xlabel('Epoch')
            ax2.set_ylabel('Validation Loss')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 3. 最终验证损失对比
            ax3 = axes[0, 2]
            names = list(successful_tests.keys())
            final_losses = [successful_tests[name]['final_val_loss'] for name in names]
            bars = ax3.bar(names, final_losses, alpha=0.7)
            ax3.set_title('最终验证损失对比')
            ax3.set_ylabel('Final Validation Loss')
            ax3.tick_params(axis='x', rotation=45)

            # 为每个柱子添加数值标签
            for bar, loss in zip(bars, final_losses):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(final_losses)*0.01,
                        f'{loss:.4f}', ha='center', va='bottom', fontsize=9)

            # 4. 收敛速度对比
            ax4 = axes[1, 0]
            convergence_speeds = [successful_tests[name]['convergence_speed'] for name in names]
            bars = ax4.bar(names, convergence_speeds, alpha=0.7, color='orange')
            ax4.set_title('收敛速度对比 (Epochs)')
            ax4.set_ylabel('Convergence Speed (Epochs)')
            ax4.tick_params(axis='x', rotation=45)

            for bar, speed in zip(bars, convergence_speeds):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(convergence_speeds)*0.01,
                        f'{speed}', ha='center', va='bottom', fontsize=9)

            # 5. 训练时间对比
            ax5 = axes[1, 1]
            training_times = [successful_tests[name]['training_time_seconds'] for name in names]
            bars = ax5.bar(names, training_times, alpha=0.7, color='green')
            ax5.set_title('训练时间对比 (秒)')
            ax5.set_ylabel('Training Time (seconds)')
            ax5.tick_params(axis='x', rotation=45)

            for bar, time_val in zip(bars, training_times):
                ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(training_times)*0.01,
                        f'{time_val:.1f}', ha='center', va='bottom', fontsize=9)

            # 6. 综合得分对比
            ax6 = axes[1, 2]
            if 'comparison' in self.results and 'scores' in self.results['comparison']:
                scores = self.results['comparison']['scores']
                score_names = list(scores.keys())
                score_values = list(scores.values())
                bars = ax6.bar(score_names, score_values, alpha=0.7, color='purple')
                ax6.set_title('综合得分对比')
                ax6.set_ylabel('Composite Score')
                ax6.tick_params(axis='x', rotation=45)

                for bar, score in zip(bars, score_values):
                    ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(score_values)*0.01,
                            f'{score}', ha='center', va='bottom', fontsize=9)

            plt.tight_layout()

            # 保存图表
            chart_path = os.path.join(self.results_dir, 'optimizer_comparison_chart.png')
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()

            logging.info(f"可视化报告已保存到: {chart_path}")

        except Exception as e:
            logging.error(f"生成可视化报告失败: {str(e)}")

    def save_test_results(self):
        """保存测试结果"""
        results_file = os.path.join(self.results_dir, 'test_results.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)

        # 保存简化的CSV报告
        csv_file = os.path.join(self.results_dir, 'optimizer_summary.csv')
        self.save_csv_summary(csv_file)

        logging.info(f"测试结果已保存到: {results_file}")
        logging.info(f"CSV摘要已保存到: {csv_file}")

    def save_csv_summary(self, csv_file: str):
        """保存CSV格式的测试摘要"""
        try:
            successful_tests = {k: v for k, v in self.results.items()
                               if isinstance(v, dict) and v.get('success', False)}

            if not successful_tests:
                return

            # 创建DataFrame
            data = []
            for name, result in successful_tests.items():
                data.append({
                    'optimizer': name,
                    'final_val_loss': result['final_val_loss'],
                    'convergence_epoch': result['convergence_epoch'],
                    'convergence_speed': result['convergence_speed'],
                    'training_time_seconds': result['training_time_seconds'],
                    'val_loss_stability': result['val_loss_stability'],
                    'memory_delta_mb': result['memory_delta_mb']
                })

            df = pd.DataFrame(data)
            df.to_csv(csv_file, index=False, encoding='utf-8')

        except Exception as e:
            logging.error(f"保存CSV摘要失败: {str(e)}")


# 测试配置示例
DEFAULT_TEST_CONFIG = {
    'learning_rate': 1e-4,
    'batch_size': 32,
    'epochs': 50,
    'loss': 'mse',
    'metrics': ['mae'],
    'test_name': 'financial_time_series_optimizer_comparison'
}
