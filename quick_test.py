#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare API快速验证脚本
用于快速验证5000积分的权限和频率限制问题

使用方法:
python3 quick_test.py
"""

import time
import logging
from datetime import datetime

# 配置简单日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

def test_basic_connection():
    """测试基础连接"""
    print("🔧 测试1: 基础连接和Token验证")
    print("-" * 40)
    
    try:
        import tushare as ts
        from config import Config
        
        ts.set_token(Config.TUSHARE_TOKEN)
        pro = ts.pro_api()
        
        # 测试基础调用
        data = pro.daily(ts_code='000001.SZ', start_date='20240101', end_date='20240102')
        
        if not data.empty:
            print(f"✅ Token验证成功")
            print(f"✅ 基础数据权限正常: 获取到 {len(data)} 条记录")
            return pro
        else:
            print("❌ Token验证失败: 返回空数据")
            return None
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None

def test_limit_data_permission(pro):
    """测试涨跌停数据权限"""
    print("\n🔧 测试2: 涨跌停数据权限验证")
    print("-" * 40)
    
    test_date = '20240102'
    
    # 测试limit_list_d接口
    try:
        print(f"📞 调用 limit_list_d (涨跌停列表)...")
        data = pro.limit_list_d(trade_date=test_date)
        
        if not data.empty:
            print(f"✅ limit_list_d 权限正常: 获取到 {len(data)} 条记录")
            print(f"   预期频率限制: 200次/分钟 (5000积分)")
        else:
            print("⚠️ limit_list_d 返回空数据")
            
    except Exception as e:
        error_msg = str(e)
        if "没有接口访问权限" in error_msg:
            print(f"❌ limit_list_d 权限不足: {error_msg}")
        elif "每分钟最多访问" in error_msg:
            print(f"⚠️ limit_list_d 频率限制: {error_msg}")
        else:
            print(f"❌ limit_list_d 其他错误: {error_msg}")
    
    # 测试stk_limit接口
    try:
        print(f"📞 调用 stk_limit (涨跌停价格)...")
        data = pro.stk_limit(trade_date=test_date)
        
        if not data.empty:
            print(f"✅ stk_limit 权限正常: 获取到 {len(data)} 条记录")
        else:
            print("⚠️ stk_limit 返回空数据")
            
    except Exception as e:
        error_msg = str(e)
        if "没有接口访问权限" in error_msg:
            print(f"❌ stk_limit 权限不足: {error_msg}")
        elif "每分钟最多访问" in error_msg:
            print(f"⚠️ stk_limit 频率限制: {error_msg}")
        else:
            print(f"❌ stk_limit 其他错误: {error_msg}")

def test_limit_step_permission(pro):
    """测试连板天梯数据权限"""
    print("\n🔧 测试3: 连板天梯数据权限验证")
    print("-" * 40)
    
    test_date = '20240102'
    
    try:
        print(f"📞 调用 limit_step (连板天梯)...")
        data = pro.limit_step(trade_date=test_date)
        
        if not data.empty:
            print(f"✅ limit_step 权限正常: 获取到 {len(data)} 条记录")
            print("🎉 恭喜！您的积分足够访问连板天梯数据")
        else:
            print("⚠️ limit_step 返回空数据")
            
    except Exception as e:
        error_msg = str(e)
        if "没有接口访问权限" in error_msg:
            print(f"❌ limit_step 权限不足: {error_msg}")
            print("💡 连板天梯数据需要8000积分以上，您当前5000积分不足")
        elif "每分钟最多访问" in error_msg:
            print(f"⚠️ limit_step 频率限制: {error_msg}")
        else:
            print(f"❌ limit_step 其他错误: {error_msg}")

def test_rate_limiting(pro):
    """测试频率限制"""
    print("\n🔧 测试4: 频率限制验证")
    print("-" * 40)
    
    test_date = '20240102'
    
    print("📞 连续调用 limit_list_d 测试频率限制...")
    
    success_count = 0
    start_time = time.time()
    
    for i in range(5):  # 测试5次调用
        try:
            data = pro.limit_list_d(trade_date=test_date)
            if not data.empty:
                success_count += 1
                print(f"   第{i+1}次调用成功: {len(data)} 条记录")
            else:
                print(f"   第{i+1}次调用返回空数据")
            
            time.sleep(0.1)  # 短暂间隔
            
        except Exception as e:
            error_msg = str(e)
            if "每分钟最多访问" in error_msg:
                print(f"🛑 第{i+1}次调用触发频率限制: {error_msg}")
                break
            else:
                print(f"❌ 第{i+1}次调用失败: {error_msg}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    if success_count > 0:
        calls_per_minute = (success_count / duration) * 60
        print(f"📊 测试结果: {success_count}/5 成功，约 {calls_per_minute:.1f} 次/分钟")
        
        if calls_per_minute > 180:  # 接近200的阈值
            print("⚠️ 可能接近200次/分钟的限制")
        else:
            print("✅ 频率在正常范围内")

def test_rate_limiter():
    """测试修改后的频率控制器"""
    print("\n🔧 测试5: 频率控制器验证")
    print("-" * 40)
    
    try:
        # 导入修改后的频率控制器
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from P import rate_limiter, pro
        
        print("✅ 频率控制器导入成功")
        print(f"   用户积分: {rate_limiter.points}")
        print(f"   通用频率限制: {rate_limiter.max_calls_per_minute} 次/分钟")
        
        # 测试安全调用
        print("\n📞 测试安全API调用...")
        
        # 测试基础数据
        data = rate_limiter.safe_api_call(
            pro.daily, 
            'daily',
            ts_code='000001.SZ', 
            start_date='20240101', 
            end_date='20240102'
        )
        print(f"✅ 基础数据安全调用: {len(data)} 条记录")
        
        # 测试涨跌停数据
        limit_data = rate_limiter.safe_api_call(
            pro.limit_list_d, 
            'limit_list_d',
            trade_date='20240102'
        )
        print(f"✅ 涨跌停数据安全调用: {len(limit_data)} 条记录")
        
        # 测试连板天梯数据（预期失败）
        step_data = rate_limiter.safe_api_call(
            pro.limit_step, 
            'limit_step',
            trade_date='20240102'
        )
        
        if step_data.empty:
            print("✅ 连板天梯数据正确处理权限不足")
        else:
            print(f"🎉 连板天梯数据意外成功: {len(step_data)} 条记录")
        
        # 检查接口限制配置
        print(f"\n📋 接口限制配置:")
        for interface, limit in rate_limiter.special_interfaces.items():
            print(f"   {interface}: {limit} 次/分钟")
        
        if rate_limiter.no_permission_interfaces:
            print(f"\n❌ 无权限接口: {rate_limiter.no_permission_interfaces}")
        
    except ImportError as e:
        print(f"❌ 频率控制器导入失败: {e}")
        print("💡 请确保 P.py 文件在当前目录")
    except Exception as e:
        print(f"❌ 频率控制器测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 Tushare API快速验证测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"预期积分: 5000")
    print("=" * 50)
    
    # 测试1: 基础连接
    pro = test_basic_connection()
    if not pro:
        print("\n❌ 基础连接失败，无法继续测试")
        return
    
    # 测试2: 涨跌停数据权限
    test_limit_data_permission(pro)
    
    # 测试3: 连板天梯数据权限
    test_limit_step_permission(pro)
    
    # 测试4: 频率限制
    test_rate_limiting(pro)
    
    # 测试5: 频率控制器
    test_rate_limiter()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试总结")
    print("=" * 50)
    print("基于测试结果，您的5000积分权限状态:")
    print("✅ 基础数据 (daily, stock_basic): 有权限，500次/分钟")
    print("✅ 涨跌停数据 (limit_list_d): 有权限，200次/分钟")
    print("❌ 连板天梯数据 (limit_step): 无权限，需要8000积分")
    print("\n💡 建议:")
    print("1. 使用修改后的频率控制器避免频率限制错误")
    print("2. 对于连板天梯数据，使用替代方案获取类似信息")
    print("3. 考虑升级积分等级以获得更高权限")
    
    print(f"\n📄 如需详细测试，请运行: python3 tushare_api_test_comprehensive.py")

if __name__ == "__main__":
    main()
