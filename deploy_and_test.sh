#!/bin/bash
# Tushare API修复方案部署和测试脚本

echo "🚀 Tushare API修复方案部署和测试"
echo "=================================="

# 配置变量
SERVER_IP="***************"
KEY_PATH="/Users/<USER>/Downloads/P.pem"
REMOTE_USER="ubuntu"
REMOTE_PATH="/home/<USER>"

# 检查密钥文件
if [ ! -f "$KEY_PATH" ]; then
    echo "❌ 密钥文件不存在: $KEY_PATH"
    exit 1
fi

echo "📁 准备上传文件..."

# 要上传的文件列表
FILES=(
    "P.pull.py"
    "quick_test.py"
    "tushare_api_test_comprehensive.py"
    "云服务器测试指南.md"
)

# 检查文件是否存在
for file in "${FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 文件不存在: $file"
        exit 1
    fi
done

echo "✅ 所有文件检查完成"

# 上传文件
echo "📤 上传文件到云服务器..."
for file in "${FILES[@]}"; do
    echo "   上传 $file..."
    scp -i "$KEY_PATH" "$file" "$REMOTE_USER@$SERVER_IP:$REMOTE_PATH/"
    if [ $? -eq 0 ]; then
        echo "   ✅ $file 上传成功"
    else
        echo "   ❌ $file 上传失败"
        exit 1
    fi
done

echo "🎉 所有文件上传完成！"

# 创建远程测试脚本
echo "📝 创建远程测试脚本..."
cat > remote_test.sh << 'EOF'
#!/bin/bash
echo "🔧 在云服务器上执行测试..."
echo "=============================="

# 检查Python环境
echo "🐍 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查必要的包
echo "📦 检查Python包..."
python3 -c "import tushare, pandas, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 缺少必要的包，尝试安装..."
    pip3 install tushare pandas numpy
fi

# 检查配置文件
if [ ! -f "config.py" ]; then
    echo "⚠️ config.py 不存在，请确保Tushare token配置正确"
fi

echo ""
echo "🚀 开始快速测试..."
echo "=================="
python3 quick_test.py

echo ""
echo "📊 测试完成！"
echo "============"
echo "如需详细测试，请运行:"
echo "python3 tushare_api_test_comprehensive.py"
echo ""
echo "查看测试指南:"
echo "cat 云服务器测试指南.md"
EOF

# 上传测试脚本
echo "📤 上传测试脚本..."
scp -i "$KEY_PATH" remote_test.sh "$REMOTE_USER@$SERVER_IP:$REMOTE_PATH/"

# 执行远程测试
echo ""
echo "🔗 连接到云服务器并执行测试..."
echo "================================"

ssh -i "$KEY_PATH" "$REMOTE_USER@$SERVER_IP" << 'ENDSSH'
cd /home/<USER>
chmod +x remote_test.sh
./remote_test.sh
ENDSSH

echo ""
echo "🎯 部署和测试完成！"
echo "=================="
echo ""
echo "📋 下一步操作："
echo "1. 查看上面的测试结果"
echo "2. 如需详细测试，登录服务器运行："
echo "   ssh -i $KEY_PATH $REMOTE_USER@$SERVER_IP"
echo "   python3 tushare_api_test_comprehensive.py"
echo ""
echo "3. 查看测试报告："
echo "   cat tushare_test_report.log"
echo "   cat tushare_test_report.json"
echo ""
echo "🔧 如果测试成功，您的主程序现在应该能够："
echo "   ✅ 自动处理API频率限制"
echo "   ✅ 优雅处理权限不足的接口"
echo "   ✅ 提供详细的错误日志"
