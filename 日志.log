交易日）
2025-08-04 13:19:34,933 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:35,279 - INFO - ✅ limit_list_d 调用成功，返回 75 条记录
2025-08-04 13:19:35,280 - INFO - 获取20250512涨跌停数据: 75条
2025-08-04 13:19:35,595 - INFO - ✅ limit_list_d 调用成功，返回 69 条记录
2025-08-04 13:19:35,596 - INFO - 获取20250513涨跌停数据: 69条
2025-08-04 13:19:35,841 - INFO - ✅ limit_list_d 调用成功，返回 84 条记录
2025-08-04 13:19:35,842 - INFO - 获取20250514涨跌停数据: 84条
2025-08-04 13:19:36,117 - INFO - ✅ limit_list_d 调用成功，返回 74 条记录
2025-08-04 13:19:36,118 - INFO - 获取20250515涨跌停数据: 74条
2025-08-04 13:19:36,365 - INFO - ✅ limit_list_d 调用成功，返回 87 条记录
2025-08-04 13:19:36,365 - INFO - 获取20250516涨跌停数据: 87条
2025-08-04 13:19:36,567 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:36,802 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:37,064 - INFO - ✅ limit_list_d 调用成功，返回 106 条记录
2025-08-04 13:19:37,064 - INFO - 获取20250519涨跌停数据: 106条
2025-08-04 13:19:37,388 - INFO - ✅ limit_list_d 调用成功，返回 115 条记录
2025-08-04 13:19:37,388 - INFO - 获取20250520涨跌停数据: 115条
2025-08-04 13:19:37,687 - INFO - ✅ limit_list_d 调用成功，返回 77 条记录
2025-08-04 13:19:37,688 - INFO - 获取20250521涨跌停数据: 77条
2025-08-04 13:19:38,014 - INFO - ✅ limit_list_d 调用成功，返回 79 条记录
2025-08-04 13:19:38,014 - INFO - 获取20250522涨跌停数据: 79条
2025-08-04 13:19:38,427 - INFO - ✅ limit_list_d 调用成功，返回 75 条记录
2025-08-04 13:19:38,427 - INFO - 获取20250523涨跌停数据: 75条
2025-08-04 13:19:38,940 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:39,369 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:39,941 - INFO - ✅ limit_list_d 调用成功，返回 86 条记录
2025-08-04 13:19:39,941 - INFO - 获取20250526涨跌停数据: 86条
2025-08-04 13:19:40,479 - INFO - ✅ limit_list_d 调用成功，返回 88 条记录
2025-08-04 13:19:40,479 - INFO - 获取20250527涨跌停数据: 88条
2025-08-04 13:19:40,912 - INFO - ✅ limit_list_d 调用成功，返回 83 条记录
2025-08-04 13:19:40,913 - INFO - 获取20250528涨跌停数据: 83条
2025-08-04 13:19:41,334 - INFO - ✅ limit_list_d 调用成功，返回 129 条记录
2025-08-04 13:19:41,335 - INFO - 获取20250529涨跌停数据: 129条
2025-08-04 13:19:41,783 - INFO - ✅ limit_list_d 调用成功，返回 79 条记录
2025-08-04 13:19:41,784 - INFO - 获取20250530涨跌停数据: 79条
2025-08-04 13:19:42,099 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:42,442 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:42,830 - INFO - ✅ limit_list_d 调用成功，返回 79 条记录
2025-08-04 13:19:42,830 - INFO - 获取20250602涨跌停数据: 79条
2025-08-04 13:19:43,198 - INFO - ✅ limit_list_d 调用成功，返回 105 条记录
2025-08-04 13:19:43,199 - INFO - 获取20250603涨跌停数据: 105条
2025-08-04 13:19:43,575 - INFO - ✅ limit_list_d 调用成功，返回 98 条记录
2025-08-04 13:19:43,575 - INFO - 获取20250604涨跌停数据: 98条
2025-08-04 13:19:43,936 - INFO - ✅ limit_list_d 调用成功，返回 94 条记录
2025-08-04 13:19:43,936 - INFO - 获取20250605涨跌停数据: 94条
2025-08-04 13:19:44,402 - INFO - ✅ limit_list_d 调用成功，返回 78 条记录
2025-08-04 13:19:44,403 - INFO - 获取20250606涨跌停数据: 78条
2025-08-04 13:19:44,798 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:45,262 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:45,736 - INFO - ✅ limit_list_d 调用成功，返回 104 条记录
2025-08-04 13:19:45,736 - INFO - 获取20250609涨跌停数据: 104条
2025-08-04 13:19:46,165 - INFO - ✅ limit_list_d 调用成功，返回 77 条记录
2025-08-04 13:19:46,166 - INFO - 获取20250610涨跌停数据: 77条
2025-08-04 13:19:46,667 - INFO - ✅ limit_list_d 调用成功，返回 86 条记录
2025-08-04 13:19:46,667 - INFO - 获取20250611涨跌停数据: 86条
2025-08-04 13:19:47,091 - INFO - ✅ limit_list_d 调用成功，返回 82 条记录
2025-08-04 13:19:47,092 - INFO - 获取20250612涨跌停数据: 82条
2025-08-04 13:19:47,462 - INFO - ✅ limit_list_d 调用成功，返回 73 条记录
2025-08-04 13:19:47,462 - INFO - 获取20250613涨跌停数据: 73条
2025-08-04 13:19:47,908 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:48,360 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:48,782 - INFO - ✅ limit_list_d 调用成功，返回 81 条记录
2025-08-04 13:19:48,782 - INFO - 获取20250616涨跌停数据: 81条
2025-08-04 13:19:49,123 - INFO - ✅ limit_list_d 调用成功，返回 72 条记录
2025-08-04 13:19:49,123 - INFO - 获取20250617涨跌停数据: 72条
2025-08-04 13:19:49,487 - INFO - ✅ limit_list_d 调用成功，返回 67 条记录
2025-08-04 13:19:49,487 - INFO - 获取20250618涨跌停数据: 67条
2025-08-04 13:19:49,902 - INFO - ✅ limit_list_d 调用成功，返回 60 条记录
2025-08-04 13:19:49,902 - INFO - 获取20250619涨跌停数据: 60条
2025-08-04 13:19:50,255 - INFO - ✅ limit_list_d 调用成功，返回 69 条记录
2025-08-04 13:19:50,255 - INFO - 获取20250620涨跌停数据: 69条
2025-08-04 13:19:50,541 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:50,871 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:51,325 - INFO - ✅ limit_list_d 调用成功，返回 87 条记录
2025-08-04 13:19:51,325 - INFO - 获取20250623涨跌停数据: 87条
2025-08-04 13:19:51,674 - INFO - ✅ limit_list_d 调用成功，返回 109 条记录
2025-08-04 13:19:51,674 - INFO - 获取20250624涨跌停数据: 109条
2025-08-04 13:19:52,025 - INFO - ✅ limit_list_d 调用成功，返回 88 条记录
2025-08-04 13:19:52,025 - INFO - 获取20250625涨跌停数据: 88条
2025-08-04 13:19:52,390 - INFO - ✅ limit_list_d 调用成功，返回 88 条记录
2025-08-04 13:19:52,391 - INFO - 获取20250626涨跌停数据: 88条
2025-08-04 13:19:52,856 - INFO - ✅ limit_list_d 调用成功，返回 83 条记录
2025-08-04 13:19:52,857 - INFO - 获取20250627涨跌停数据: 83条
2025-08-04 13:19:53,355 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:53,787 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:54,263 - INFO - ✅ limit_list_d 调用成功，返回 97 条记录
2025-08-04 13:19:54,264 - INFO - 获取20250630涨跌停数据: 97条
2025-08-04 13:19:54,724 - INFO - ✅ limit_list_d 调用成功，返回 96 条记录
2025-08-04 13:19:54,725 - INFO - 获取20250701涨跌停数据: 96条
2025-08-04 13:19:55,207 - INFO - ✅ limit_list_d 调用成功，返回 84 条记录
2025-08-04 13:19:55,207 - INFO - 获取20250702涨跌停数据: 84条
2025-08-04 13:19:55,644 - INFO - ✅ limit_list_d 调用成功，返回 78 条记录
2025-08-04 13:19:55,644 - INFO - 获取20250703涨跌停数据: 78条
2025-08-04 13:19:56,109 - INFO - ✅ limit_list_d 调用成功，返回 72 条记录
2025-08-04 13:19:56,109 - INFO - 获取20250704涨跌停数据: 72条
2025-08-04 13:19:56,521 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:56,933 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:57,338 - INFO - ✅ limit_list_d 调用成功，返回 80 条记录
2025-08-04 13:19:57,338 - INFO - 获取20250707涨跌停数据: 80条
2025-08-04 13:19:57,691 - INFO - ✅ limit_list_d 调用成功，返回 83 条记录
2025-08-04 13:19:57,691 - INFO - 获取20250708涨跌停数据: 83条
2025-08-04 13:19:58,049 - INFO - ✅ limit_list_d 调用成功，返回 86 条记录
2025-08-04 13:19:58,050 - INFO - 获取20250709涨跌停数据: 86条
2025-08-04 13:19:58,346 - INFO - ✅ limit_list_d 调用成功，返回 87 条记录
2025-08-04 13:19:58,346 - INFO - 获取20250710涨跌停数据: 87条
2025-08-04 13:19:58,624 - INFO - ✅ limit_list_d 调用成功，返回 94 条记录
2025-08-04 13:19:58,624 - INFO - 获取20250711涨跌停数据: 94条
2025-08-04 13:19:59,053 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:59,456 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:19:59,881 - INFO - ✅ limit_list_d 调用成功，返回 93 条记录
2025-08-04 13:19:59,881 - INFO - 获取20250714涨跌停数据: 93条
2025-08-04 13:20:00,344 - INFO - ✅ limit_list_d 调用成功，返回 81 条记录
2025-08-04 13:20:00,344 - INFO - 获取20250715涨跌停数据: 81条
2025-08-04 13:20:01,068 - INFO - ✅ limit_list_d 调用成功，返回 80 条记录
2025-08-04 13:20:01,069 - INFO - 获取20250716涨跌停数据: 80条
2025-08-04 13:20:01,689 - INFO - ✅ limit_list_d 调用成功，返回 86 条记录
2025-08-04 13:20:01,689 - INFO - 获取20250717涨跌停数据: 86条
2025-08-04 13:20:02,428 - INFO - ✅ limit_list_d 调用成功，返回 67 条记录
2025-08-04 13:20:02,428 - INFO - 获取20250718涨跌停数据: 67条
2025-08-04 13:20:03,175 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:20:03,832 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:20:04,469 - INFO - ✅ limit_list_d 调用成功，返回 143 条记录
2025-08-04 13:20:04,470 - INFO - 获取20250721涨跌停数据: 143条
2025-08-04 13:20:05,102 - INFO - ✅ limit_list_d 调用成功，返回 121 条记录
2025-08-04 13:20:05,102 - INFO - 获取20250722涨跌停数据: 121条
2025-08-04 13:20:05,769 - INFO - ✅ limit_list_d 调用成功，返回 111 条记录
2025-08-04 13:20:05,770 - INFO - 获取20250723涨跌停数据: 111条
2025-08-04 13:20:06,492 - INFO - ✅ limit_list_d 调用成功，返回 92 条记录
2025-08-04 13:20:06,492 - INFO - 获取20250724涨跌停数据: 92条
2025-08-04 13:20:07,213 - INFO - ✅ limit_list_d 调用成功，返回 82 条记录
2025-08-04 13:20:07,213 - INFO - 获取20250725涨跌停数据: 82条
2025-08-04 13:20:07,729 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:20:08,285 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:20:08,799 - INFO - ✅ limit_list_d 调用成功，返回 79 条记录
2025-08-04 13:20:08,799 - INFO - 获取20250728涨跌停数据: 79条
2025-08-04 13:20:09,193 - INFO - ✅ limit_list_d 调用成功，返回 67 条记录
2025-08-04 13:20:09,194 - INFO - 获取20250729涨跌停数据: 67条
2025-08-04 13:20:09,694 - INFO - ✅ limit_list_d 调用成功，返回 74 条记录
2025-08-04 13:20:09,694 - INFO - 获取20250730涨跌停数据: 74条
2025-08-04 13:20:10,051 - INFO - ✅ limit_list_d 调用成功，返回 77 条记录
2025-08-04 13:20:10,052 - INFO - 获取20250731涨跌停数据: 77条
2025-08-04 13:20:10,437 - INFO - ✅ limit_list_d 调用成功，返回 76 条记录
2025-08-04 13:20:10,437 - INFO - 获取20250801涨跌停数据: 76条
2025-08-04 13:20:10,884 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:20:11,440 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:20:11,954 - INFO - ℹ️ limit_list_d 返回空数据（可能是非交易日）
2025-08-04 13:20:11,977 - INFO - ✅ 总共获取到历史涨跌停数据: 17435条
2025-08-04 13:20:11,977 - INFO - 基于涨跌停数据构建连板天梯信息...
2025-08-04 13:20:12,010 - INFO - ✅ 基于涨跌停数据构建连板天梯数据: 9602条
2025-08-04 13:20:12,012 - INFO - 连板分布统计:
step
1.0     7378
2.0     1323
3.0      464
4.0      237
5.0      106
6.0       52
7.0       22
8.0       10
9.0        7
10.0       3
Name: count, dtype: int64
2025-08-04 13:20:13,274 - INFO - 获取到开盘啦榜单数据: 8000条
2025-08-04 13:20:13,749 - INFO - 获取资金流数据 时间范围: 20250101 - 20250804
2025-08-04 13:20:13,749 - INFO - 尝试获取全市场资金流数据...
2025-08-04 13:20:14,657 - INFO - 成功获取全市场资金流数据，过滤后共 6000 条记录
2025-08-04 13:20:14,657 - INFO - 成功获取资金流数据，共 6000 条记录
2025-08-04 13:20:14,657 - INFO - 获取筹码数据 时间范围: 20250101 - 20250804
2025-08-04 13:21:31,745 - INFO - 高效处理技术指标和特征
2025-08-04 13:21:31,745 - INFO - 处理前的列名: ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'pre_close', 'change', 'pct_chg', 'vol', 'amount', 'adj_factor', 'turnover_rate', 'pe_ttm', 'pb', 'float_share', 'limit_up']
2025-08-04 13:21:31,796 - INFO - 开始高效预处理 5419 只股票数据
2025-08-04 13:21:32,647 - INFO - 并行处理股票特征，使用8个处理核心
2025-08-04 13:24:56,735 - WARNING - 处理后缺少以下特征: ['net_mf_amount']
2025-08-04 13:24:57,115 - INFO - 已添加前一天涨停标记 (pre_limit_up)
2025-08-04 13:24:57,115 - INFO - 添加大盘指数特征
2025-08-04 13:24:57,128 - INFO - market_index_data 列名: ['ts_code', 'trade_date', 'close', 'pct_chg']
2025-08-04 13:24:59,355 - INFO - 将额外的数据特征添加到主数据集中
2025-08-04 13:24:59,356 - INFO - 添加缺失的资金流特征列 buy_sm_amount 并填充默认值 0
2025-08-04 13:24:59,357 - INFO - 添加缺失的资金流特征列 sell_sm_amount 并填充默认值 0
2025-08-04 13:24:59,358 - INFO - 添加缺失的资金流特征列 buy_md_amount 并填充默认值 0
2025-08-04 13:24:59,358 - INFO - 添加缺失的资金流特征列 sell_md_amount 并填充默认值 0
2025-08-04 13:24:59,359 - INFO - 添加缺失的资金流特征列 buy_lg_amount 并填充默认值 0
2025-08-04 13:24:59,360 - INFO - 添加缺失的资金流特征列 sell_lg_amount 并填充默认值 0
2025-08-04 13:24:59,360 - INFO - 添加缺失的资金流特征列 sell_elg_amount 并填充默认值 0
2025-08-04 13:24:59,360 - WARNING - 🔧 处理缺失的资金流特征: ['buy_sm_amount', 'sell_sm_amount', 'buy_md_amount', 'sell_md_amount', 'buy_lg_amount', 'sell_lg_amount', 'sell_elg_amount']
2025-08-04 13:25:00,074 - INFO - ✅ 成功合并涨跌停数据: 17435条
2025-08-04 13:25:00,788 - INFO - ✅ 成功合并连板天梯数据: 9602条
2025-08-04 13:25:02,635 - INFO - 开始添加筹码特征
2025-08-04 13:25:02,635 - WARNING - 使用默认值填充筹码特征
2025-08-04 13:25:02,635 - WARNING - 筹码数据缺失，跳过筹码相关特征（避免使用假数据误导模型）
2025-08-04 13:25:02,639 - INFO - 添加特征后的数据量: (328708, 230)
2025-08-04 13:25:02,639 - INFO - 添加特征后数据列名: ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'pre_close', 'change', 'pct_chg', 'vol', 'amount', 'adj_factor', 'turnover_rate', 'pe_ttm', 'pb', 'float_share', 'limit_up', 'market_code', 'max_pct', 'limit_up_price', 'turnover_rate_ma5', 'turnover_rate_ma10', 'turnover_rate_ma20', 'turnover_rate_change', 'turnover_rate_acc', 'turnover_rate_rank_20d', 'unusual_volume', 'turnover_q20', 'turnover_q40', 'turnover_q60', 'turnover_q80', 'turnover_oscillator', 'ma3', 'ma5', 'ma10', 'ma20', 'ma60', 'volume_ma3', 'volume_ma5', 'volume_ma10', 'boll_upper', 'boll_middle', 'boll_lower', 'boll_bandwidth', 'boll_pct_b', 'sar', 'sar_direction', 'emv', 'emv_ma14', 'cr', 'cr_ma1', 'cr_ma2', 'cr_ma3', 'price_trend_short', 'price_trend_mid', 'price_trend_long', 'trend_strength', 'wr_6', 'wr_10', 'wr_14', 'wr_overbought', 'wr_oversold', 'buy_elg_amount', '封单金额比', '封单金额比_3日平均', 'mfi', 'mfi_overbought', 'mfi_oversold', '连续涨停天数', '近期涨停次数', '连板强度_变异系数', '断板压力指数', '最近涨停距离', '涨停强度', '涨停打板时间指数', '涨停回封强度', '涨停打开压力', '开盘涨停', 'atr', 'daily_volatility', 'volatility_ma5', '振幅', '振幅_ma5', '振幅_ma10', '振幅_rank', '真实振幅', '量比', '量比变化率', '量比强度', '周开盘价', '周收盘价', '周最高价', '周最低价', '周成交量', '周涨跌幅', '周K_强度', '月开盘价', '月收盘价', '月最高价', '月最低价', '月成交量', '月涨跌幅', '月K_强度', '周RSI', '月RSI', '周量比', '月量比', 'GK波动率', '日内高低比', '日内高低比变化', 'volatility_acc', 'volatility_acc_5', '极端波动', 'boll_position', 'channel_breakthrough', 'volume_ratio', 'volume_trend', 'volume_explosion', 'volume_shrink', '量价背离', 'rsi2', 'rsi6', 'rsi14', 'rsi', 'rsi_short_overbought', 'rsi_short_oversold', 'rsi_trend_up', 'macd', 'macd_signal', 'macd_hist', 'macd_cross_up', 'MACD底背离', 'kdj_k', 'kdj_d', 'cci', 'cci_extremes', 'adx', 'dmi_plus', 'dmi_minus', 'adx_strong_trend', 'dmi_bull', 'upper_shadow', 'lower_shadow', 'body_size', '早晨之星', '看涨吞没', '孕线形态', '涨停双响炮', '地天板', '反包板', '波浪调整末期', 'TD_Buy_Setup', 'TD_Sell_Setup', 'TD反转信号', 'OBV', 'OBV_MA10', 'OBV背离', '主力净流入占比', '主力净流入MA5', '机构建仓信号', 'VWAP', 'VWAP_穿越', '筹码松散度', '高位成交占比_5日', '唐奇安上轨', '唐奇安下轨', '唐奇安通道突破', '关键阻力位_20日', '关键支撑位_20日', '距上方阻力比例', '距下方支撑比例', '阻力突破回踩', '三重动量确认', '盘中炸板再涨停', '涨停前兆', '涨停合成概率', '最近一次涨停偏离度', '超短反转信号', '连板接力概率', '竞价异动', '承接力度', '首板历史成功率', '连板成功率', '北交所强度指标', '科创板活跃度', '创业板短线强度', '跨市场资金流动', '日内波动幅度', '短线交易热度', '抢筹强度', '涨停开板天数比', '波段交易信号', 'is_valid_signal', 'net_mf_amount', 'pre_limit_up', '000001.SH_close', '000001.SH_pct_chg', '399001.SZ_close', '399001.SZ_pct_chg', '399005.SZ_close', '399005.SZ_pct_chg', '399006.SZ_close', '399006.SZ_pct_chg', 'buy_sm_amount', 'sell_sm_amount', 'buy_md_amount', 'sell_md_amount', 'buy_lg_amount', 'sell_lg_amount', 'sell_elg_amount', 'limit_times', 'open_times', 'step', 'is_on_kpl_list', 'hot', 'is_on_ths_hot', 'buy_sm_amount_mf', 'sell_sm_amount_mf', 'buy_md_amount_mf', 'sell_md_amount_mf', 'buy_lg_amount_mf', 'sell_lg_amount_mf', 'buy_elg_amount_mf', 'sell_elg_amount_mf', 'net_mf_amount_mf', 'weight_avg_change', 'winner_rate_change', 'cost_spread', 'cost_concentration', 'cost_pressure']
2025-08-04 13:25:02,639 - INFO - 获取到 328708 条股票数据
2025-08-04 13:25:02,639 - INFO - 🔒 目标变量将在安全的数据分割流程中创建，避免数据泄漏
2025-08-04 13:25:04,734 - WARNING - 获取 20250804 的行情数据为空
2025-08-04 13:25:05,328 - INFO - 20250803 不是交易日，尝试前一天
2025-08-04 13:25:05,883 - INFO - 20250802 不是交易日，尝试前一天
2025-08-04 13:25:07,124 - INFO - 成功获取 20250801 的收盘价和名称，共 5410 条记录
2025-08-04 13:25:08.556347: I tensorflow/compiler/xla/stream_executor/cuda/cuda_gpu_executor.cc:894] successful NUMA node read from SysFS had negative value (-1), but there must be at least one NUMA node, so returning NUMA node zero. See more at https://github.com/torvalds/linux/blob/v6.0/Documentation/ABI/testing/sysfs-bus-pci#L344-L355
2025-08-04 13:25:08.558067: W tensorflow/core/common_runtime/gpu/gpu_device.cc:2211] Cannot dlopen some GPU libraries. Please make sure the missing libraries mentioned above are installed properly if you would like to use GPU. Follow the guide at https://www.tensorflow.org/install/gpu for how to download and setup the required libraries for your platform.
Skipping registering GPU devices...
2025-08-04 13:25:10,767 - INFO - 内存清理完成:
清理前: 1732.1MB
清理后: 1709.5MB
释放: 22.5MB
2025-08-04 13:25:10,767 - INFO - 数据预处理完成，共处理 328708 条记录
2025-08-04 13:25:10,767 - INFO - 正在准备特征数据...
2025-08-04 13:25:13,955 - INFO - 内存清理完成:
清理前: 1742.9MB
清理后: 1717.2MB
释放: 25.7MB
2025-08-04 13:25:13,955 - INFO - 特征数据准备完成
2025-08-04 13:25:13,955 - INFO - 开始模型训练...
2025-08-04 13:25:13,955 - INFO - 初始化safe_learning_rate变量
2025-08-04 13:25:13,955 - INFO - safe_learning_rate初始值: 0.005
2025-08-04 13:25:13,956 - INFO - 开始训练模型
2025-08-04 13:25:13,956 - INFO - 
=== 开始 首板 策略训练 ===
2025-08-04 13:25:14,132 - INFO - 🔒 开始安全准备首板策略数据（无数据泄漏版本）
2025-08-04 13:25:15,578 - INFO - ✅ trade_date列已标准化为YYYY-MM-DD格式
2025-08-04 13:25:16,152 - INFO - ✅ 过滤后数据集: 328708行, 开始日期: 2025-01-01
2025-08-04 13:25:16,152 - INFO - 🔒 步骤2: 严格按时间进行数据分割（防止数据泄漏）
2025-08-04 13:25:17,156 - INFO - 🔒 时间分割结果:
2025-08-04 13:25:17,156 - INFO -   训练集: 94374行 (2025-06-03之前)
2025-08-04 13:25:17,156 - INFO -   验证集: 113661行 (2025-06-03~2025-07-02)
2025-08-04 13:25:17,156 - INFO -   测试集: 120673行 (2025-07-02之后)
2025-08-04 13:25:17,156 - INFO - 🔒 步骤3: 只基于训练集计算安全特征
2025-08-04 13:25:17,303 - INFO - 🔒 步骤4: 为首板策略安全生成目标变量
2025-08-04 13:25:17,692 - INFO - 🔒 添加安全的首板策略特征...
2025-08-04 13:25:17,992 - INFO - 🔒 从训练集中安全选择首板样本...
2025-08-04 13:25:18,002 - INFO - 📊 首板样本详细统计:
2025-08-04 13:25:18,002 - INFO -   训练集总数据: 94374行
2025-08-04 13:25:18,003 - INFO -   训练集首板样本: 962个 (1.02%)
2025-08-04 13:25:18,015 - INFO -   验证集总数据: 113661行
2025-08-04 13:25:18,015 - INFO -   验证集首板样本: 1526个 (1.34%)
2025-08-04 13:25:18,015 - INFO -   测试集总数据: 120673行
2025-08-04 13:25:18,015 - INFO -   测试集首板样本: 2157个 (1.79%)
2025-08-04 13:25:18,015 - INFO - ✅ 首板样本收集完成，总计4645个样本
2025-08-04 13:25:18,015 - INFO - 📊 首板策略正负样本分布统计:
2025-08-04 13:25:18,019 - INFO -   ✅ 正样本(首板成功): 252个 (47.7%)
2025-08-04 13:25:18,019 - INFO -   ❌ 负样本(首板失败): 276个 (52.3%)
2025-08-04 13:25:18,019 - INFO -   ⚪ 中性样本: 4117个
2025-08-04 13:25:18,019 - INFO -   📈 样本平衡比例: 252:276 (正:负)
2025-08-04 13:25:18,019 - INFO -   ✅ 样本平衡性良好 (平衡度: 0.91)
2025-08-04 13:25:18,020 - INFO - 🔒 步骤5: 准备最终的训练数据
2025-08-04 13:25:18,020 - INFO - ✅ 首板策略总样本数: 4645
2025-08-04 13:25:18,022 - INFO - 训练集目标变量分布:
future_1_day_limit_up
False    808
True     154
Name: count, dtype: int64
2025-08-04 13:25:18,022 - INFO - 🔒 步骤6: 准备序列数据（时间序列格式）
2025-08-04 13:25:18,024 - INFO - ✅ 使用pe_ttm作为pe特征
2025-08-04 13:25:18,024 - INFO - ✅ 使用13个有效特征
2025-08-04 13:25:18,024 - INFO - 🔧 开始生成序列数据，输入数据: 4645行, 序列长度: 5
2025-08-04 13:25:18,024 - INFO - 🔧 有效特征数量: 13
2025-08-04 13:25:20,270 - INFO - 🔒 步骤7: 执行最终的数据分割和返回
2025-08-04 13:25:20,270 - INFO - 🔧 数组长度统计: X=1, y1_cls=57, y1_reg=1, y2_cls=57, y2_reg=1, weights=1, 最小长度=1
2025-08-04 13:25:20,270 - INFO - ✅ 所有数组长度已统一为: 1
2025-08-04 13:25:20,270 - INFO - ✅ 最终数据形状: X=(1, 5, 13), 样本权重=1
2025-08-04 13:25:20,271 - INFO - 🔧 开始正确的时间序列分割...
2025-08-04 13:25:20,271 - INFO - 🔧 时间序列分割点:
2025-08-04 13:25:20,271 - INFO -   训练集: 2025-01-02 到 2025-06-23
2025-08-04 13:25:20,271 - INFO -   验证集: 2025-06-24 到 2025-07-09
2025-08-04 13:25:20,271 - INFO -   测试集: 2025-07-10 到 2025-08-01
2025-08-04 13:25:22,523 - INFO - 🔒 三分法安全分割完成:
2025-08-04 13:25:22,523 - INFO -   训练集: (1, 5, 13)
2025-08-04 13:25:22,523 - INFO -   验证集: (9, 5, 13)
2025-08-04 13:25:22,523 - INFO -   测试集: (47, 5, 13)
2025-08-04 13:25:22,523 - INFO - 
📊 训练集详细样本统计 (首板策略):
2025-08-04 13:25:22,523 - INFO -   总样本数: 1
2025-08-04 13:25:22,523 - INFO -   分类输出1:
2025-08-04 13:25:22,524 - INFO -     负样本(值=0): 1个 (100.0%)
2025-08-04 13:25:22,524 - INFO -   回归输出1:
2025-08-04 13:25:22,524 - INFO -     有效样本: 1个 (缺失: 0个)
2025-08-04 13:25:22,524 - INFO -     均值: 0.647%
2025-08-04 13:25:22,524 - INFO -     标准差: 0.000%
2025-08-04 13:25:22,524 - INFO -     范围: [0.647%, 0.647%]
2025-08-04 13:25:22,524 - INFO -     中位数: 0.647%
2025-08-04 13:25:22,524 - INFO -   分类输出2:
2025-08-04 13:25:22,524 - INFO -     负样本(值=0): 1个 (100.0%)
2025-08-04 13:25:22,524 - INFO -   回归输出2:
2025-08-04 13:25:22,524 - INFO -     有效样本: 1个 (缺失: 0个)
2025-08-04 13:25:22,525 - INFO -     均值: 0.647%
2025-08-04 13:25:22,525 - INFO -     标准差: 0.000%
2025-08-04 13:25:22,525 - INFO -     范围: [0.647%, 0.647%]
2025-08-04 13:25:22,525 - INFO -     中位数: 0.647%
2025-08-04 13:25:22,525 - INFO - 
📊 验证集详细样本统计 (首板策略):
2025-08-04 13:25:22,525 - INFO -   总样本数: 9
2025-08-04 13:25:22,525 - INFO -   分类输出1:
2025-08-04 13:25:22,525 - INFO -     正样本(值=1): 9个 (100.0%)
2025-08-04 13:25:22,525 - INFO -   回归输出1:
2025-08-04 13:25:22,525 - INFO -     有效样本: 9个 (缺失: 0个)
2025-08-04 13:25:22,525 - INFO -     均值: 0.000%
2025-08-04 13:25:22,525 - INFO -     标准差: 0.000%
2025-08-04 13:25:22,525 - INFO -     范围: [0.000%, 0.000%]
2025-08-04 13:25:22,526 - INFO -     中位数: 0.000%
2025-08-04 13:25:22,526 - INFO -   分类输出2:
2025-08-04 13:25:22,526 - INFO -     正样本(值=1): 9个 (100.0%)
2025-08-04 13:25:22,526 - INFO -   回归输出2:
2025-08-04 13:25:22,526 - INFO -     有效样本: 9个 (缺失: 0个)
2025-08-04 13:25:22,526 - INFO -     均值: 0.000%
2025-08-04 13:25:22,526 - INFO -     标准差: 0.000%
2025-08-04 13:25:22,526 - INFO -     范围: [0.000%, 0.000%]
2025-08-04 13:25:22,526 - INFO -     中位数: 0.000%
2025-08-04 13:25:22,526 - INFO - 
📊 测试集详细样本统计 (首板策略):
2025-08-04 13:25:22,526 - INFO -   总样本数: 47
2025-08-04 13:25:22,526 - INFO -   分类输出1:
2025-08-04 13:25:22,526 - INFO -     正样本(值=1): 47个 (100.0%)
2025-08-04 13:25:22,526 - INFO -   回归输出1:
2025-08-04 13:25:22,527 - INFO -     有效样本: 47个 (缺失: 0个)
2025-08-04 13:25:22,527 - INFO -     均值: 0.000%
2025-08-04 13:25:22,527 - INFO -     标准差: 0.000%
2025-08-04 13:25:22,527 - INFO -     范围: [0.000%, 0.000%]
2025-08-04 13:25:22,527 - INFO -     中位数: 0.000%
2025-08-04 13:25:22,527 - INFO -   分类输出2:
2025-08-04 13:25:22,527 - INFO -     正样本(值=1): 47个 (100.0%)
2025-08-04 13:25:22,527 - INFO -   回归输出2:
2025-08-04 13:25:22,527 - INFO -     有效样本: 47个 (缺失: 0个)
2025-08-04 13:25:22,527 - INFO -     均值: 0.000%
2025-08-04 13:25:22,527 - INFO -     标准差: 0.000%
2025-08-04 13:25:22,527 - INFO -     范围: [0.000%, 0.000%]
2025-08-04 13:25:22,527 - INFO -     中位数: 0.000%
2025-08-04 13:25:22,528 - INFO - 🔒 检查样本分布质量...
2025-08-04 13:25:22,528 - INFO - 训练集 classification_output_1分布: {0: 1}
2025-08-04 13:25:22,528 - INFO - 训练集 classification_output_2分布: {0: 1}
2025-08-04 13:25:22,528 - INFO - 验证集 classification_output_1分布: {0: 0, 1: 9}
2025-08-04 13:25:22,528 - INFO - 验证集 classification_output_2分布: {0: 0, 1: 9}
2025-08-04 13:25:22,528 - INFO - 测试集 classification_output_1分布: {0: 0, 1: 47}
2025-08-04 13:25:22,529 - INFO - 测试集 classification_output_2分布: {0: 0, 1: 47}
2025-08-04 13:25:22,529 - ERROR - ❌ 样本分布质量检查失败:
2025-08-04 13:25:22,529 - ERROR -   - 训练集的classification_output_1只有一个类别: [0]
2025-08-04 13:25:22,529 - ERROR -   - 训练集的classification_output_1少数类样本过少: 1
2025-08-04 13:25:22,529 - ERROR -   - 训练集的classification_output_2只有一个类别: [0]
2025-08-04 13:25:22,529 - ERROR -   - 训练集的classification_output_2少数类样本过少: 1
2025-08-04 13:25:22,529 - ERROR -   - 验证集的classification_output_1只有一个类别: [1]
2025-08-04 13:25:22,529 - ERROR -   - 验证集的classification_output_2只有一个类别: [1]
2025-08-04 13:25:22,529 - ERROR -   - 测试集的classification_output_1只有一个类别: [1]
2025-08-04 13:25:22,529 - ERROR -   - 测试集的classification_output_2只有一个类别: [1]
2025-08-04 13:25:22,529 - WARNING - ⚠️ 数据质量检查未通过，但继续训练
2025-08-04 13:25:22,529 - INFO -   classification_output_1 - 训练集分布: [1], 验证集分布: [0 9], 测试集分布: [ 0 47]
2025-08-04 13:25:22,529 - INFO -   classification_output_2 - 训练集分布: [1], 验证集分布: [0 9], 测试集分布: [ 0 47]
2025-08-04 13:25:22,529 - INFO - ✅ 首板策略数据准备完成，耗时: 8.40秒
2025-08-04 13:25:22,610 - INFO - 特征维度: 13个特征，5个时间步
2025-08-04 13:25:22,610 - INFO - ✅ 已将字典格式标签转换为列表格式用于超参数优化
2025-08-04 13:25:22,610 - INFO - 开始首板策略超参数优化...
2025-08-04 13:25:22,612 - INFO - 元学习初始化完成 | 有效记录: 0条
[I 2025-08-04 13:25:22,613] A new study created in memory with name: no-name-1f6ed762-beda-4ac3-afdc-cf085c767010
2025-08-04 13:25:22,616 - WARNING - 测试集样本量 (47) 过小，可能导致评估不稳定
2025-08-04 13:25:22,616 - WARNING - 验证集数据问题，尝试重新分割
[W 2025-08-04 13:25:22,616] Trial 0 failed with parameters: {'param_strategy': 'history', 'lstm_units_1': 192, 'batch_size': 64, 'learning_rate': 0.01220776478695415, 'dropout_rate': 0.30000000000000004, 'l2_reg': 0.0006796578090758161, 'patience': 5, 'attention_heads': 12, 'num_experts_1': 7, 'expert_units_1': 32, 'num_experts_2': 2, 'expert_units_2': 16, 'combined_weight': 0.3042422429595377, 'base_learning_rate': 3.752055855124284e-05, 'kt_num_experts': 4, 'kt_expert_units': 60} because of the following error: TypeError('list indices must be integers or slices, not str').
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/optuna/study/_optimize.py", line 201, in _run_trial
    value_or_values = func(trial)
  File "/home/<USER>/P.pull.py", line 5221, in objective
    y_cls1 = np.concatenate([y_train['classification_output_1'],
TypeError: list indices must be integers or slices, not str
[W 2025-08-04 13:25:22,617] Trial 0 failed with value None.
2025-08-04 13:25:22,798 - ERROR - 超参数优化失败: list indices must be integers or slices, not str
2025-08-04 13:25:22,802 - INFO - 已更新首板策略元学习历史记录，当前记录数: 1
2025-08-04 13:25:22,802 - ERROR - 获取最佳模型失败: No trials are completed yet.
2025-08-04 13:25:22,802 - ERROR - 清理试验资源时出错: No trials are completed yet.
2025-08-04 13:25:22,802 - INFO - 优化完成，最佳验证损失: inf
2025-08-04 13:25:22,802 - INFO - 超参数优化耗时: 0.19秒
2025-08-04 13:25:22,803 - INFO - 最佳参数已保存到 models/首板_best_params.pkl
2025-08-04 13:25:22,803 - INFO - 首板策略最佳超参数（完整）:
2025-08-04 13:25:22,803 - INFO -   lstm_units_1: 128
2025-08-04 13:25:22,803 - INFO -   lstm_units_2: 64
2025-08-04 13:25:22,803 - INFO -   attention_heads: 8
2025-08-04 13:25:22,803 - INFO -   dropout_rate: 0.2
2025-08-04 13:25:22,803 - INFO -   l2_reg: 0.001
2025-08-04 13:25:22,803 - INFO -   learning_rate: 0.0005
2025-08-04 13:25:22,803 - INFO -   batch_size: 128
2025-08-04 13:25:22,803 - INFO -   patience: 10
2025-08-04 13:25:22,803 - INFO -   num_experts_1: 4
2025-08-04 13:25:22,803 - INFO -   expert_units_1: 64
2025-08-04 13:25:22,803 - INFO -   num_experts_2: 2
2025-08-04 13:25:22,803 - INFO -   expert_units_2: 32
2025-08-04 13:25:22,803 - INFO - 首板策略最佳超参数JSON格式: {
  "lstm_units_1": 128,
  "lstm_units_2": 64,
  "attention_heads": 8,
  "dropout_rate": 0.2,
  "l2_reg": 0.001,
  "learning_rate": 0.0005,
  "batch_size": 128,
  "patience": 10,
  "num_experts_1": 4,
  "expert_units_1": 64,
  "num_experts_2": 2,
  "expert_units_2": 32
}
2025-08-04 13:25:22,803 - INFO - 首板策略最佳超参数: {'lstm_units_1': 128, 'lstm_units_2': 64, 'attention_heads': 8, 'dropout_rate': 0.2, 'l2_reg': 0.001, 'learning_rate': 0.0005, 'batch_size': 128, 'patience': 10, 'num_experts_1': 4, 'expert_units_1': 64, 'num_experts_2': 2, 'expert_units_2': 32}
2025-08-04 13:25:22,803 - INFO - 开始首板策略模型部署...
2025-08-04 13:25:22,804 - INFO - 超参数优化过程未返回模型，构建新模型...
2025-08-04 13:25:24,104 - INFO - regression_output_1稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,105 - INFO - regression_output_2稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,106 - INFO - regression_output_1稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,106 - INFO - regression_output_2稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,107 - INFO - regression_output_1稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,108 - INFO - regression_output_2稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,108 - INFO - ✅ 已将字典格式标签转换为列表格式，匹配模型输出顺序
2025-08-04 13:25:24,227 - WARNING - 策略 '首板' 初始训练失败: in user code:

    File "/home/<USER>/.local/lib/python3.10/site-packages/keras/src/engine/training.py", line 1377, in train_function  *
        return step_function(self, iterator)
    File "/home/<USER>/.local/lib/python3.10/site-packages/keras/src/engine/training.py", line 1360, in step_function  **
        outputs = model.distribute_strategy.run(run_step, args=(data,))
    File "/home/<USER>/.local/lib/python3.10/site-packages/keras/src/engine/training.py", line 1349, in run_step  **
        outputs = model.train_step(data)
    File "/home/<USER>/.local/lib/python3.10/site-packages/keras/src/engine/training.py", line 1126, in train_step
        y_pred = self(x, training=True)
    File "/home/<USER>/.local/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 70, in error_handler
        raise e.with_traceback(filtered_tb) from None
    File "/home/<USER>/.local/lib/python3.10/site-packages/keras/src/engine/input_spec.py", line 298, in assert_input_compatibility
        raise ValueError(

    ValueError: Input 0 of layer "model" is incompatible with the layer: expected shape=(None, 5, 163), found shape=(None, 5, 13)
. 尝试使用更小批量...
2025-08-04 13:25:24,227 - INFO - 尝试使用更小的批量大小重新训练...
2025-08-04 13:25:24,228 - INFO - regression_output_1稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,229 - INFO - regression_output_2稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,229 - INFO - regression_output_1稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,230 - INFO - regression_output_2稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:24,298 - ERROR - 策略 '首板' 重试训练失败: Input 0 of layer "model" is incompatible with the layer: expected shape=(None, 5, 163), found shape=(1, 5, 13)
2025-08-04 13:25:24,512 - INFO - 
=== 开始 连板 策略训练 ===
2025-08-04 13:25:24,708 - INFO - 🔒 开始安全准备连板策略数据（无数据泄漏版本）
2025-08-04 13:25:26,293 - INFO - ✅ trade_date列已标准化为YYYY-MM-DD格式
2025-08-04 13:25:26,901 - INFO - ✅ 过滤后数据集: 328708行, 开始日期: 2025-01-01
2025-08-04 13:25:26,901 - INFO - 🔒 步骤2: 严格按时间进行数据分割（防止数据泄漏）
2025-08-04 13:25:27,821 - INFO - 🔒 时间分割结果:
2025-08-04 13:25:27,822 - INFO -   训练集: 94374行 (2025-06-03之前)
2025-08-04 13:25:27,822 - INFO -   验证集: 113661行 (2025-06-03~2025-07-02)
2025-08-04 13:25:27,822 - INFO -   测试集: 120673行 (2025-07-02之后)
2025-08-04 13:25:27,822 - INFO - 🔒 步骤3: 只基于训练集计算安全特征
2025-08-04 13:25:27,927 - INFO - 🔒 步骤4: 为连板策略安全生成目标变量
2025-08-04 13:25:28,216 - INFO - 🔒 处理连板策略（安全版本）...
2025-08-04 13:25:28,636 - INFO - ✅ 训练集中找到1356个有效连板样本
2025-08-04 13:25:28,636 - INFO - ✅ 验证集中找到2932个连板样本
2025-08-04 13:25:28,636 - INFO - ✅ 测试集中找到4317个连板样本
2025-08-04 13:25:28,641 - INFO - 🔒 步骤5: 准备最终的训练数据
2025-08-04 13:25:28,641 - INFO - ✅ 连板策略总样本数: 8605
2025-08-04 13:25:28,642 - INFO - 🔒 步骤6: 准备序列数据（时间序列格式）
2025-08-04 13:25:28,643 - INFO - ✅ 使用pe_ttm作为pe特征
2025-08-04 13:25:28,643 - INFO - ✅ 使用13个有效特征
2025-08-04 13:25:28,644 - INFO - 🔧 开始生成序列数据，输入数据: 8605行, 序列长度: 5
2025-08-04 13:25:28,644 - INFO - 🔧 有效特征数量: 13
2025-08-04 13:25:32,682 - INFO - 🔒 步骤7: 执行最终的数据分割和返回
2025-08-04 13:25:32,682 - INFO - 🔧 数组长度统计: X=452, y1_cls=4607, y1_reg=452, y2_cls=4607, y2_reg=452, weights=452, 最小长度=452
2025-08-04 13:25:32,683 - INFO - ✅ 所有数组长度已统一为: 452
2025-08-04 13:25:32,683 - INFO - ✅ 最终数据形状: X=(452, 5, 13), 样本权重=452
2025-08-04 13:25:32,683 - INFO - 🔧 开始正确的时间序列分割...
2025-08-04 13:25:32,684 - INFO - 🔧 时间序列分割点:
2025-08-04 13:25:32,684 - INFO -   训练集: 2025-01-03 到 2025-06-27
2025-08-04 13:25:32,684 - INFO -   验证集: 2025-06-30 到 2025-07-14
2025-08-04 13:25:32,684 - INFO -   测试集: 2025-07-15 到 2025-08-01
2025-08-04 13:25:37,551 - INFO - 🔒 三分法安全分割完成:
2025-08-04 13:25:37,551 - INFO -   训练集: (1585, 5, 13)
2025-08-04 13:25:37,551 - INFO -   验证集: (1237, 5, 13)
2025-08-04 13:25:37,551 - INFO -   测试集: (1785, 5, 13)
2025-08-04 13:25:37,551 - INFO - 
📊 训练集详细样本统计 (连板策略):
2025-08-04 13:25:37,551 - INFO -   总样本数: 1585
2025-08-04 13:25:37,551 - INFO -   分类输出1:
2025-08-04 13:25:37,551 - INFO -     负样本(值=0): 1325个 (83.6%)
2025-08-04 13:25:37,551 - INFO -     正样本(值=1): 260个 (16.4%)
2025-08-04 13:25:37,551 - INFO -     🔴 样本严重不平衡 (平衡度: 0.20)
2025-08-04 13:25:37,552 - INFO -   回归输出1:
2025-08-04 13:25:37,552 - INFO -     有效样本: 1585个 (缺失: 0个)
2025-08-04 13:25:37,552 - INFO -     均值: -0.312%
2025-08-04 13:25:37,552 - INFO -     标准差: 2.773%
2025-08-04 13:25:37,552 - INFO -     范围: [-10.507%, 10.967%]
2025-08-04 13:25:37,552 - INFO -     中位数: 0.000%
2025-08-04 13:25:37,552 - INFO -   分类输出2:
2025-08-04 13:25:37,552 - INFO -     负样本(值=0): 1325个 (83.6%)
2025-08-04 13:25:37,553 - INFO -     正样本(值=1): 260个 (16.4%)
2025-08-04 13:25:37,553 - INFO -     🔴 样本严重不平衡 (平衡度: 0.20)
2025-08-04 13:25:37,553 - INFO -   回归输出2:
2025-08-04 13:25:37,553 - INFO -     有效样本: 1585个 (缺失: 0个)
2025-08-04 13:25:37,553 - INFO -     均值: -0.312%
2025-08-04 13:25:37,553 - INFO -     标准差: 2.773%
2025-08-04 13:25:37,553 - INFO -     范围: [-10.507%, 10.967%]
2025-08-04 13:25:37,553 - INFO -     中位数: 0.000%
2025-08-04 13:25:37,553 - INFO - 
📊 验证集详细样本统计 (连板策略):
2025-08-04 13:25:37,553 - INFO -   总样本数: 1237
2025-08-04 13:25:37,554 - INFO -   分类输出1:
2025-08-04 13:25:37,554 - INFO -     负样本(值=0): 791个 (63.9%)
2025-08-04 13:25:37,554 - INFO -     正样本(值=1): 446个 (36.1%)
2025-08-04 13:25:37,554 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.56)
2025-08-04 13:25:37,554 - INFO -   回归输出1:
2025-08-04 13:25:37,554 - INFO -     有效样本: 1237个 (缺失: 0个)
2025-08-04 13:25:37,554 - INFO -     均值: 0.000%
2025-08-04 13:25:37,554 - INFO -     标准差: 0.000%
2025-08-04 13:25:37,554 - INFO -     范围: [0.000%, 0.000%]
2025-08-04 13:25:37,554 - INFO -     中位数: 0.000%
2025-08-04 13:25:37,554 - INFO -   分类输出2:
2025-08-04 13:25:37,555 - INFO -     负样本(值=0): 791个 (63.9%)
2025-08-04 13:25:37,555 - INFO -     正样本(值=1): 446个 (36.1%)
2025-08-04 13:25:37,555 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.56)
2025-08-04 13:25:37,555 - INFO -   回归输出2:
2025-08-04 13:25:37,555 - INFO -     有效样本: 1237个 (缺失: 0个)
2025-08-04 13:25:37,555 - INFO -     均值: 0.000%
2025-08-04 13:25:37,555 - INFO -     标准差: 0.000%
2025-08-04 13:25:37,555 - INFO -     范围: [0.000%, 0.000%]
2025-08-04 13:25:37,555 - INFO -     中位数: 0.000%
2025-08-04 13:25:37,555 - INFO - 
📊 测试集详细样本统计 (连板策略):
2025-08-04 13:25:37,555 - INFO -   总样本数: 1785
2025-08-04 13:25:37,556 - INFO -   分类输出1:
2025-08-04 13:25:37,556 - INFO -     负样本(值=0): 1255个 (70.3%)
2025-08-04 13:25:37,556 - INFO -     正样本(值=1): 530个 (29.7%)
2025-08-04 13:25:37,556 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.42)
2025-08-04 13:25:37,556 - INFO -   回归输出1:
2025-08-04 13:25:37,556 - INFO -     有效样本: 1785个 (缺失: 0个)
2025-08-04 13:25:37,556 - INFO -     均值: 0.000%
2025-08-04 13:25:37,556 - INFO -     标准差: 0.000%
2025-08-04 13:25:37,556 - INFO -     范围: [0.000%, 0.000%]
2025-08-04 13:25:37,556 - INFO -     中位数: 0.000%
2025-08-04 13:25:37,556 - INFO -   分类输出2:
2025-08-04 13:25:37,557 - INFO -     负样本(值=0): 1255个 (70.3%)
2025-08-04 13:25:37,557 - INFO -     正样本(值=1): 530个 (29.7%)
2025-08-04 13:25:37,557 - INFO -     ⚠️ 样本轻度不平衡 (平衡度: 0.42)
2025-08-04 13:25:37,557 - INFO -   回归输出2:
2025-08-04 13:25:37,557 - INFO -     有效样本: 1785个 (缺失: 0个)
2025-08-04 13:25:37,557 - INFO -     均值: 0.000%
2025-08-04 13:25:37,557 - INFO -     标准差: 0.000%
2025-08-04 13:25:37,557 - INFO -     范围: [0.000%, 0.000%]
2025-08-04 13:25:37,557 - INFO -     中位数: 0.000%
2025-08-04 13:25:37,557 - INFO - 🔒 检查样本分布质量...
2025-08-04 13:25:37,557 - INFO - 训练集 classification_output_1分布: {0: 1325, 1: 260}
2025-08-04 13:25:37,558 - INFO - 训练集 classification_output_2分布: {0: 1325, 1: 260}
2025-08-04 13:25:37,558 - INFO - 验证集 classification_output_1分布: {0: 791, 1: 446}
2025-08-04 13:25:37,558 - INFO - 验证集 classification_output_2分布: {0: 791, 1: 446}
2025-08-04 13:25:37,558 - INFO - 测试集 classification_output_1分布: {0: 1255, 1: 530}
2025-08-04 13:25:37,558 - INFO - 测试集 classification_output_2分布: {0: 1255, 1: 530}
2025-08-04 13:25:37,559 - INFO - ✅ 样本分布质量检查通过
2025-08-04 13:25:37,559 - INFO -   classification_output_1 - 训练集分布: [1325  260], 验证集分布: [791 446], 测试集分布: [1255  530]
2025-08-04 13:25:37,559 - INFO -   classification_output_2 - 训练集分布: [1325  260], 验证集分布: [791 446], 测试集分布: [1255  530]
2025-08-04 13:25:37,560 - INFO - ✅ 连板策略数据准备完成，耗时: 12.85秒
2025-08-04 13:25:37,641 - INFO - 特征维度: 13个特征，5个时间步
2025-08-04 13:25:37,642 - INFO - ✅ 已将字典格式标签转换为列表格式用于超参数优化
2025-08-04 13:25:37,642 - INFO - 开始连板策略超参数优化...
2025-08-04 13:25:37,644 - INFO - 元学习初始化完成 | 有效记录: 0条
[I 2025-08-04 13:25:37,644] A new study created in memory with name: no-name-70e6b282-a0e7-4aae-a18b-1b1789000a17
2025-08-04 13:25:37,648 - INFO - 专家网络单元数 32 小于 连板 策略建议值 64，但保持超参数优化器的选择
2025-08-04 13:25:39,083 - INFO - 编译模型配置，策略类型: 连板, 学习率: 1.1415338890724785e-05
2025-08-04 13:25:39,084 - INFO - 使用AdEMAMix优化器 - 2024年最新技术，混合双EMA设计
2025-08-04 13:25:39,106 - INFO - 监控指标已设置为动态匹配val_classification_output_1_auc，ReduceLROnPlateau已禁用，学习率由LearningRateScheduler全权控制
2025-08-04 13:25:39,107 - INFO - regression_output_1稳健标准化后：均值=-3.1188，中位数=0.0000，标准差=27.7291，最小值=-105.0690，最大值=109.6740
2025-08-04 13:25:39,108 - INFO - regression_output_1稳健标准化后：均值=-0.9524，中位数=0.0000，标准差=7.1893，最小值=-15.0000，最大值=15.0000
2025-08-04 13:25:39,108 - INFO - regression_output_2稳健标准化后：均值=-3.1188，中位数=0.0000，标准差=27.7291，最小值=-105.0690，最大值=109.6740
2025-08-04 13:25:39,109 - INFO - regression_output_2稳健标准化后：均值=-0.9524，中位数=0.0000，标准差=7.1893，最小值=-15.0000，最大值=15.0000
2025-08-04 13:25:39,110 - INFO - regression_output_1稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:39,110 - INFO - regression_output_2稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:39,240 - WARNING - 无法找到与 val_classification_output_1_auc 匹配的指标，使用 val_loss 代替
Epoch 1/30
[W 2025-08-04 13:25:40,530] Trial 0 failed with parameters: {'param_strategy': 'history', 'lstm_units_1': 192, 'batch_size': 64, 'learning_rate': 0.01220776478695415, 'dropout_rate': 0.30000000000000004, 'l2_reg': 0.0006796578090758161, 'patience': 5, 'attention_heads': 12, 'num_experts_1': 8, 'expert_units_1': 32, 'num_experts_2': 2, 'expert_units_2': 16, 'combined_weight': 0.3042422429595377, 'base_learning_rate': 3.752055855124284e-05, 'kt_num_experts': 4, 'kt_expert_units': 60} because of the following error: InvalidArgumentError().
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/optuna/study/_optimize.py", line 201, in _run_trial
    value_or_values = func(trial)
  File "/home/<USER>/P.pull.py", line 5587, in objective
    history = model.fit(
  File "/home/<USER>/.local/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/P.pull.py", line 4772, in apply_gradients
    long_ema.assign(self.beta_long * long_ema + (1 - self.beta_long) * grad)
tensorflow.python.framework.errors_impl.InvalidArgumentError: {{function_node __wrapped__AddV2_device_/job:localhost/replica:0/task:0/device:CPU:0}} Incompatible shapes: [96,32] vs. [384,16] [Op:AddV2] name: 
[W 2025-08-04 13:25:40,530] Trial 0 failed with value None.
2025-08-04 13:25:40,741 - ERROR - 超参数优化失败: {{function_node __wrapped__AddV2_device_/job:localhost/replica:0/task:0/device:CPU:0}} Incompatible shapes: [96,32] vs. [384,16] [Op:AddV2] name: 
2025-08-04 13:25:40,747 - INFO - 已更新连板策略元学习历史记录，当前记录数: 1
2025-08-04 13:25:40,747 - ERROR - 获取最佳模型失败: No trials are completed yet.
2025-08-04 13:25:40,747 - ERROR - 清理试验资源时出错: No trials are completed yet.
2025-08-04 13:25:40,747 - INFO - 优化完成，最佳验证损失: inf
2025-08-04 13:25:40,747 - INFO - 超参数优化耗时: 3.11秒
2025-08-04 13:25:40,748 - INFO - 最佳参数已保存到 models/连板_best_params.pkl
2025-08-04 13:25:40,748 - INFO - 连板策略最佳超参数（完整）:
2025-08-04 13:25:40,748 - INFO -   lstm_units_1: 256
2025-08-04 13:25:40,748 - INFO -   lstm_units_2: 64
2025-08-04 13:25:40,748 - INFO -   attention_heads: 12
2025-08-04 13:25:40,748 - INFO -   dropout_rate: 0.3
2025-08-04 13:25:40,748 - INFO -   l2_reg: 0.001
2025-08-04 13:25:40,748 - INFO -   learning_rate: 0.0005
2025-08-04 13:25:40,748 - INFO -   batch_size: 128
2025-08-04 13:25:40,748 - INFO -   patience: 10
2025-08-04 13:25:40,748 - INFO -   num_experts_1: 6
2025-08-04 13:25:40,748 - INFO -   expert_units_1: 96
2025-08-04 13:25:40,748 - INFO -   num_experts_2: 2
2025-08-04 13:25:40,748 - INFO -   expert_units_2: 32
2025-08-04 13:25:40,749 - INFO - 连板策略最佳超参数JSON格式: {
  "lstm_units_1": 256,
  "lstm_units_2": 64,
  "attention_heads": 12,
  "dropout_rate": 0.3,
  "l2_reg": 0.001,
  "learning_rate": 0.0005,
  "batch_size": 128,
  "patience": 10,
  "num_experts_1": 6,
  "expert_units_1": 96,
  "num_experts_2": 2,
  "expert_units_2": 32
}
2025-08-04 13:25:40,749 - INFO - 连板策略最佳超参数: {'lstm_units_1': 256, 'lstm_units_2': 64, 'attention_heads': 12, 'dropout_rate': 0.3, 'l2_reg': 0.001, 'learning_rate': 0.0005, 'batch_size': 128, 'patience': 10, 'num_experts_1': 6, 'expert_units_1': 96, 'num_experts_2': 2, 'expert_units_2': 32}
2025-08-04 13:25:40,749 - INFO - 开始连板策略模型部署...
2025-08-04 13:25:40,749 - INFO - 超参数优化过程未返回模型，构建新模型...
2025-08-04 13:25:41,984 - INFO - regression_output_1稳健标准化后：均值=-0.1125，中位数=0.0000，标准差=1.0000，最小值=-3.7891，最大值=3.9552
2025-08-04 13:25:41,985 - INFO - regression_output_2稳健标准化后：均值=-0.1125，中位数=0.0000，标准差=1.0000，最小值=-3.7891，最大值=3.9552
2025-08-04 13:25:41,986 - INFO - regression_output_1稳健标准化后：均值=-0.0375，中位数=0.0000，标准差=0.3333，最小值=-1.2628，最大值=1.3181
2025-08-04 13:25:41,987 - INFO - regression_output_2稳健标准化后：均值=-0.0375，中位数=0.0000，标准差=0.3333，最小值=-1.2628，最大值=1.3181
2025-08-04 13:25:41,987 - INFO - regression_output_1稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:41,988 - INFO - regression_output_2稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:41,989 - INFO - ✅ 已将字典格式标签转换为列表格式，匹配模型输出顺序
2025-08-04 13:25:42,060 - WARNING - 策略 '连板' 初始训练失败: Input 0 of layer "model_1" is incompatible with the layer: expected shape=(None, 5, 163), found shape=(128, 5, 13). 尝试使用更小批量...
2025-08-04 13:25:42,060 - INFO - 尝试使用更小的批量大小重新训练...
2025-08-04 13:25:42,061 - INFO - regression_output_1稳健标准化后：均值=-0.0375，中位数=0.0000，标准差=0.3333，最小值=-1.2628，最大值=1.3181
2025-08-04 13:25:42,061 - INFO - regression_output_2稳健标准化后：均值=-0.0375，中位数=0.0000，标准差=0.3333，最小值=-1.2628，最大值=1.3181
2025-08-04 13:25:42,062 - INFO - regression_output_1稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:42,063 - INFO - regression_output_2稳健标准化后：均值=0.0000，中位数=0.0000，标准差=0.0000，最小值=0.0000，最大值=0.0000
2025-08-04 13:25:42,125 - ERROR - 策略 '连板' 重试训练失败: Input 0 of layer "model_1" is incompatible with the layer: expected shape=(None, 5, 163), found shape=(32, 5, 13)
2025-08-04 13:25:42,378 - INFO - 模型训练总耗时: 28.42秒
2025-08-04 13:25:45,392 - INFO - 内存清理完成:
清理前: 1854.8MB
清理后: 1834.7MB
释放: 20.0MB
2025-08-04 13:25:45,392 - INFO - 当前模型版本：首板v0 (2024-01-01)，连板v0 (2024-01-01)
2025-08-04 13:25:45,392 - INFO - 训练完成，分析模型文件状态...
2025-08-04 13:25:45,392 - INFO - 没有找到首板策略的模型文件
2025-08-04 13:25:45,393 - INFO - 没有找到连板策略的模型文件
2025-08-04 13:25:45,393 - INFO - 开始模型预测...
2025-08-04 13:25:45,393 - INFO - 开始首板策略选股...
2025-08-04 13:25:45,393 - ERROR - 首板策略模型不可用 (版本: v46 (2025-08-04))
2025-08-04 13:25:45,394 - INFO - 开始连板策略选股...
2025-08-04 13:25:45,394 - ERROR - 连板策略模型不可用 (版本: v41 (2025-08-04))
2025-08-04 13:25:48,183 - INFO - 内存清理完成:
清理前: 1834.7MB
清理后: 1834.7MB
释放: 0.0MB
2025-08-04 13:25:48,183 - INFO - 
==================== 首板策略选股结果 ====================
2025-08-04 13:25:48,183 - INFO - 
未选出符合首板策略的股票
2025-08-04 13:25:48,183 - INFO - 
============================================================
2025-08-04 13:25:48,183 - INFO - 
==================== 连板策略选股结果 ====================
2025-08-04 13:25:48,183 - INFO - 
未选出符合连板策略的股票
2025-08-04 13:25:48,183 - INFO - 
============================================================
2025-08-04 13:25:48,183 - INFO - 
程序运行完成:
总耗时: 630.82 秒
内存使用: 972.3MB -> 1834.7MB
2025-08-04 13:25:48,189 - INFO - 已加载历史分析数据：104 条记录
2025-08-04 13:25:49,059 - INFO - 
=== 历史表现分析报告 ===
## 基础表现指标 ##
准确率（涨停预测）:
strategy
连板    0.315789
首板    0.242424

平均绝对误差（涨幅预测）:
strategy
连板     7.250538
首板    11.779539

## 高级分析指标 ##
精确率:
strategy
连板    0.133333
首板    0.169492
召回率:
strategy
连板    1.000000
首板    0.909091
2025-08-04 13:25:51,074 - INFO - Server酱消息发送成功 (通过data.pushid验证): {'code': 0, 'message': '', 'data': {'pushid': '7940836', 'readkey': 'SCTHhM0dcirbZGx', 'error': 'SUCCESS', 'errno': 0}}
2025-08-04 13:25:53,879 - INFO - 内存清理完成:
清理前: 1844.6MB
清理后: 1842.3MB
释放: 2.3MB
2025-08-04 13:25:56,682 - INFO - 内存清理完成:
清理前: 1277.0MB
清理后: 1261.8MB
释放: 15.2MB
2025-08-04 13:25:56,683 - INFO - 程序执行完毕
ubuntu@VM-0-15-ubuntu:~$ 