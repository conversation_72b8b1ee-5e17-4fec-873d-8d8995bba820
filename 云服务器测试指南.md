# 🚀 Tushare API云服务器测试指南

## 📋 测试目标

基于官方文档验证您5000积分的实际权限和频率限制：

### **预期结果**：
- ✅ **基础数据** (stock_basic, daily): 500次/分钟
- ✅ **涨跌停数据** (limit_list_d, stk_limit): 200次/分钟  
- ❌ **连板天梯数据** (limit_step): 无权限 (需要8000积分)

## 🔧 部署步骤

### 1. 上传文件到云服务器

```bash
# 上传测试脚本
scp -i /Users/<USER>/Downloads/P.pem tushare_api_test_comprehensive.py ubuntu@***************:/home/<USER>/

# 上传修改后的主程序
scp -i /Users/<USER>/Downloads/P.pem P.pull.py ubuntu@***************:/home/<USER>/

# 上传配置文件（如果需要）
scp -i /Users/<USER>/Downloads/P.pem config.py ubuntu@***************:/home/<USER>/
```

### 2. 登录云服务器

```bash
ssh -i /Users/<USER>/Downloads/P.pem ubuntu@***************
```

### 3. 安装依赖（如果需要）

```bash
# 检查Python环境
python3 --version

# 安装必要的包
pip3 install tushare pandas numpy

# 或者如果有requirements.txt
pip3 install -r requirements.txt
```

## 🧪 执行测试

### 测试1: 综合API权限测试

```bash
# 运行综合测试
python3 tushare_api_test_comprehensive.py

# 查看实时日志
tail -f tushare_test_report.log
```

### 测试2: 验证修改后的主程序

```bash
# 测试修改后的频率控制器
python3 -c "
from P import rate_limiter, pro
import logging

# 测试基础数据
data = rate_limiter.safe_api_call(pro.daily, 'daily', ts_code='000001.SZ', start_date='20240101', end_date='20240102')
print(f'基础数据测试: {len(data)} 条记录')

# 测试涨跌停数据
limit_data = rate_limiter.safe_api_call(pro.limit_list_d, 'limit_list_d', trade_date='20240102')
print(f'涨跌停数据测试: {len(limit_data)} 条记录')

# 测试连板天梯数据（预期失败）
step_data = rate_limiter.safe_api_call(pro.limit_step, 'limit_step', trade_date='20240102')
print(f'连板天梯数据测试: {len(step_data)} 条记录')
"
```

## 📊 预期测试结果

### ✅ **成功的测试**：

1. **stock_basic (股票基础信息)**
   - 权限: ✅ 有权限
   - 频率: 500次/分钟
   - 结果: 返回所有股票列表

2. **daily (日线数据)**
   - 权限: ✅ 有权限  
   - 频率: 500次/分钟
   - 结果: 返回指定股票的日线数据

3. **limit_list_d (涨跌停列表)**
   - 权限: ✅ 有权限
   - 频率: 200次/分钟 (关键验证点)
   - 结果: 返回指定日期的涨跌停数据

### ❌ **预期失败的测试**：

1. **limit_step (连板天梯)**
   - 权限: ❌ 无权限 (需要8000积分)
   - 错误: "没有接口访问权限"
   - 结果: 返回空DataFrame

## 🔍 关键验证点

### 1. 频率限制验证

测试脚本会连续调用API，验证：
- **基础数据**: 是否能达到500次/分钟
- **涨跌停数据**: 是否在200次/分钟时触发限制

### 2. 错误处理验证

检查频率控制器是否正确处理：
- ✅ 权限不足错误
- ✅ 频率限制错误  
- ✅ 自动重试机制

### 3. 替代方案验证

当连板天梯数据无权限时：
- ✅ 优雅降级到替代方案
- ✅ 使用基础数据构建类似功能

## 📄 查看测试报告

### 1. 实时日志

```bash
# 查看实时测试日志
tail -f tushare_test_report.log

# 搜索特定信息
grep "频率限制" tushare_test_report.log
grep "权限不足" tushare_test_report.log
```

### 2. JSON报告

```bash
# 查看详细测试报告
cat tushare_test_report.json | python3 -m json.tool

# 提取关键信息
python3 -c "
import json
with open('tushare_test_report.json', 'r', encoding='utf-8') as f:
    report = json.load(f)

print('=== 测试摘要 ===')
for interface, result in report['results'].items():
    permission = '✅' if result['actual_permission'] else '❌'
    print(f'{permission} {interface}: {result[\"description\"]}')
    if result['actual_permission'] and 'calls_per_minute' in result:
        print(f'   实际频率: {result[\"calls_per_minute\"]:.1f} 次/分钟')
        print(f'   预期频率: {result[\"expected_rate_limit\"]} 次/分钟')
"
```

## 🚨 故障排除

### 问题1: 导入错误

```bash
# 检查模块是否存在
python3 -c "import tushare; print('tushare OK')"
python3 -c "from config import Config; print('config OK')"

# 如果config.py不存在，创建临时配置
echo "class Config:
    TUSHARE_TOKEN = 'your_token_here'" > config.py
```

### 问题2: 权限错误

```bash
# 检查token是否正确
python3 -c "
import tushare as ts
from config import Config
ts.set_token(Config.TUSHARE_TOKEN)
pro = ts.pro_api()
try:
    data = pro.daily(ts_code='000001.SZ', start_date='20240101', end_date='20240102')
    print(f'Token验证成功: {len(data)} 条记录')
except Exception as e:
    print(f'Token验证失败: {e}')
"
```

### 问题3: 网络问题

```bash
# 测试网络连接
ping tushare.pro

# 检查防火墙设置
sudo ufw status
```

## 📈 成功标准

测试成功的标准：

1. ✅ **基础数据权限正常**: stock_basic, daily 可以正常调用
2. ✅ **涨跌停数据权限正常**: limit_list_d 可以调用，但频率限制为200次/分钟
3. ✅ **连板数据权限不足**: limit_step 返回权限错误
4. ✅ **频率控制器工作正常**: 自动处理限制和重试
5. ✅ **错误处理完善**: 优雅处理各种错误情况

## 📞 测试完成后

测试完成后，请提供：

1. **测试日志**: `tushare_test_report.log`
2. **JSON报告**: `tushare_test_report.json`  
3. **控制台输出**: 复制粘贴关键的输出信息

这将帮助我们确认：
- ✅ 权限配置是否准确
- ✅ 频率控制器是否工作正常
- ✅ 替代方案是否可行

## 🎯 下一步

基于测试结果，我们将：

1. **优化频率控制策略**
2. **完善替代数据获取方案**  
3. **调整主程序的API调用逻辑**
4. **提供最终的生产环境配置**
