2025-08-04 11:54:51,314 - INFO - 开始金融时间序列优化器对比测试
2025-08-04 11:54:51,314 - INFO - 开始金融时间序列优化器对比测试
2025-08-04 11:54:51,314 - INFO - 生成合成金融数据: 8000样本, 25特征, 60序列长度
2025-08-04 11:54:52,708 - INFO - 数据生成完成: X.shape=(8000, 25), y.shape=(8000,)
2025-08-04 11:54:52,709 - INFO - 特征统计: mean=0.0000, std=1.0000
2025-08-04 11:54:52,710 - INFO - 目标统计: mean=0.0000, std=1.0000
2025-08-04 11:54:52,714 - INFO - 数据分割完成:
2025-08-04 11:54:52,714 - INFO -   训练集: X_train.shape=(6400, 25), y_train.shape=(6400,)
2025-08-04 11:54:52,714 - INFO -   验证集: X_val.shape=(1600, 25), y_val.shape=(1600,)
2025-08-04 11:54:52,715 - INFO - 优化器测试框架初始化完成，结果将保存到: optimizer_test_results_20250804_115452
2025-08-04 11:54:52,715 - INFO - 开始全面测试，优化器列表: ['adam', 'adamw', 'lion', 'ademamix', 'fractional']
2025-08-04 11:54:52,715 - INFO - 开始测试优化器: adam
2025-08-04 11:56:03,093 - INFO - 优化器 adam 测试完成:
2025-08-04 11:56:03,093 - INFO -   最终验证损失: 0.067100
2025-08-04 11:56:03,093 - INFO -   收敛epoch: 99
2025-08-04 11:56:03,094 - INFO -   训练时间: 70.07秒
2025-08-04 11:56:03,094 - INFO -   内存增长: 90.33MB
2025-08-04 11:56:08,436 - INFO - 开始测试优化器: adamw
2025-08-04 11:57:18,507 - INFO - 优化器 adamw 测试完成:
2025-08-04 11:57:18,507 - INFO -   最终验证损失: 0.068042
2025-08-04 11:57:18,507 - INFO -   收敛epoch: 98
2025-08-04 11:57:18,507 - INFO -   训练时间: 69.74秒
2025-08-04 11:57:18,507 - INFO -   内存增长: 43.98MB
2025-08-04 11:57:23,866 - INFO - 开始测试优化器: lion
2025-08-04 11:57:24,313 - ERROR - 优化器 lion 测试失败: BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'
2025-08-04 11:57:29,646 - INFO - 开始测试优化器: ademamix
2025-08-04 11:57:30,066 - ERROR - 优化器 ademamix 测试失败: BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'
2025-08-04 11:57:35,407 - INFO - 开始测试优化器: fractional
2025-08-04 11:57:35,822 - ERROR - 优化器 fractional 测试失败: BaseOptimizer.__init__() missing 1 required positional argument: 'learning_rate'
2025-08-04 11:57:41,173 - INFO - 测试结果已保存到: optimizer_test_results_20250804_115452/test_results.json
2025-08-04 11:57:41,173 - INFO - CSV摘要已保存到: optimizer_test_results_20250804_115452/optimizer_summary.csv
2025-08-04 11:57:45,174 - INFO - 可视化报告已保存到: optimizer_test_results_20250804_115452/optimizer_comparison_chart.png
2025-08-04 11:57:45,178 - INFO - 测试结果已保存到: financial_optimizer_test_20250804_115745
