#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare API权限和频率限制综合测试脚本
基于官方文档的准确权限信息进行测试验证

测试目标：
1. 验证5000积分的实际权限
2. 测试各接口的频率限制
3. 验证替代方案的可行性
4. 生成详细的测试报告
"""

import sys
import os
import time
import logging
import pandas as pd
from datetime import datetime, timedelta
from collections import defaultdict
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tushare_test_report.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 导入配置和API
try:
    import tushare as ts
    from config import Config
    ts.set_token(Config.TUSHARE_TOKEN)
    pro = ts.pro_api()
except ImportError as e:
    logging.error(f"导入失败: {e}")
    sys.exit(1)

class TushareAPITester:
    """Tushare API综合测试器"""
    
    def __init__(self, user_points=5000):
        self.user_points = user_points
        self.test_results = defaultdict(dict)
        self.start_time = datetime.now()
        
        # 基于官方文档的接口权限配置
        self.interface_configs = {
            # 基础数据 - 通用权限
            'stock_basic': {
                'min_points': 120,
                'rate_limit': 500,  # 5000积分通用限制
                'description': '股票基础信息'
            },
            'daily': {
                'min_points': 120,
                'rate_limit': 500,
                'description': '日线行情数据'
            },
            
            # 涨跌停数据 - 特殊限制
            'limit_list_d': {
                'min_points': 5000,
                'rate_limit': 200,  # 5000积分=200次/分钟，8000积分=500次/分钟
                'rate_limit_8000': 500,
                'description': '涨跌停列表（新）'
            },
            'stk_limit': {
                'min_points': 2000,
                'rate_limit': 200,
                'rate_limit_8000': 500,
                'description': '涨跌停价格数据'
            },
            
            # 连板天梯数据 - 高权限要求
            'limit_step': {
                'min_points': 8000,
                'rate_limit': 500,
                'description': '连板天梯数据'
            },
            
            # 财务数据
            'income': {
                'min_points': 2000,
                'rate_limit': 200,
                'description': '利润表数据'
            }
        }
        
        logging.info(f"🔧 初始化测试器 - 用户积分: {user_points}")
    
    def check_interface_permission(self, interface_name):
        """检查接口权限"""
        config = self.interface_configs.get(interface_name, {})
        min_points = config.get('min_points', 0)
        
        has_permission = self.user_points >= min_points
        expected_rate = config.get('rate_limit', 500)
        
        # 特殊处理8000积分的接口
        if 'rate_limit_8000' in config and self.user_points >= 8000:
            expected_rate = config['rate_limit_8000']
        
        return has_permission, expected_rate, config.get('description', '未知接口')
    
    def test_single_interface(self, interface_name, api_func, test_params):
        """测试单个接口"""
        logging.info(f"\n🔍 测试接口: {interface_name}")
        
        has_permission, expected_rate, description = self.check_interface_permission(interface_name)
        
        result = {
            'interface': interface_name,
            'description': description,
            'expected_permission': has_permission,
            'expected_rate_limit': expected_rate,
            'actual_permission': False,
            'actual_rate_limit': None,
            'test_time': datetime.now().isoformat(),
            'error_messages': [],
            'success_count': 0,
            'total_attempts': 0
        }
        
        if not has_permission:
            logging.warning(f"⚠️ {interface_name} 预期无权限 (需要积分: {self.interface_configs[interface_name]['min_points']})")
        
        # 执行测试
        try:
            # 单次调用测试
            logging.info(f"📞 执行单次调用测试...")
            data = api_func(**test_params)
            
            if data is not None and not data.empty:
                result['actual_permission'] = True
                result['success_count'] = 1
                logging.info(f"✅ 单次调用成功，返回 {len(data)} 条记录")
            else:
                logging.warning(f"⚠️ 单次调用返回空数据")
            
            result['total_attempts'] = 1
            
        except Exception as e:
            error_msg = str(e)
            result['error_messages'].append(error_msg)
            result['total_attempts'] = 1
            
            if "没有接口访问权限" in error_msg:
                logging.error(f"❌ 权限不足: {error_msg}")
            elif "每分钟最多访问" in error_msg:
                # 提取频率限制信息
                import re
                match = re.search(r'每分钟最多访问该接口(\d+)次', error_msg)
                if match:
                    actual_limit = int(match.group(1))
                    result['actual_rate_limit'] = actual_limit
                    logging.warning(f"🔄 发现频率限制: {actual_limit}/分钟")
            else:
                logging.error(f"❌ 其他错误: {error_msg}")
        
        self.test_results[interface_name] = result
        return result
    
    def test_rate_limiting(self, interface_name, api_func, test_params, max_calls=10):
        """测试频率限制"""
        if interface_name not in self.test_results:
            return
        
        result = self.test_results[interface_name]
        if not result['actual_permission']:
            logging.info(f"⏭️ {interface_name} 无权限，跳过频率测试")
            return
        
        logging.info(f"🚀 测试 {interface_name} 频率限制 (最多{max_calls}次调用)")
        
        success_count = 0
        start_time = time.time()
        
        for i in range(max_calls):
            try:
                data = api_func(**test_params)
                if data is not None and not data.empty:
                    success_count += 1
                    logging.info(f"📞 第{i+1}次调用成功")
                else:
                    logging.warning(f"⚠️ 第{i+1}次调用返回空数据")
                
                # 短暂间隔
                time.sleep(0.1)
                
            except Exception as e:
                error_msg = str(e)
                result['error_messages'].append(f"第{i+1}次调用: {error_msg}")
                
                if "每分钟最多访问" in error_msg:
                    logging.warning(f"🛑 第{i+1}次调用触发频率限制")
                    break
                else:
                    logging.error(f"❌ 第{i+1}次调用失败: {error_msg}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        result['success_count'] = success_count
        result['total_attempts'] = i + 1
        result['test_duration'] = duration
        result['calls_per_minute'] = (success_count / duration) * 60 if duration > 0 else 0
        
        logging.info(f"📊 频率测试结果: {success_count}/{i+1} 成功，{result['calls_per_minute']:.1f} 次/分钟")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logging.info("🚀 开始Tushare API综合测试")
        logging.info("=" * 60)
        
        # 测试配置
        test_date = '20240102'  # 使用一个确定有数据的日期
        
        test_cases = [
            # 基础数据测试
            ('stock_basic', pro.stock_basic, {'exchange': '', 'list_status': 'L'}),
            ('daily', pro.daily, {'ts_code': '000001.SZ', 'start_date': test_date, 'end_date': test_date}),
            
            # 涨跌停数据测试
            ('limit_list_d', pro.limit_list_d, {'trade_date': test_date}),
            ('stk_limit', pro.stk_limit, {'trade_date': test_date}),
            
            # 连板天梯数据测试
            ('limit_step', pro.limit_step, {'trade_date': test_date}),
            
            # 财务数据测试
            ('income', pro.income, {'ts_code': '000001.SZ', 'period': '20231231'})
        ]
        
        # 执行单次调用测试
        logging.info("\n📋 第一阶段: 权限和单次调用测试")
        for interface_name, api_func, params in test_cases:
            self.test_single_interface(interface_name, api_func, params)
            time.sleep(1)  # 避免过快调用
        
        # 执行频率限制测试（仅对有权限的接口）
        logging.info("\n📋 第二阶段: 频率限制测试")
        for interface_name, api_func, params in test_cases:
            if self.test_results[interface_name]['actual_permission']:
                self.test_rate_limiting(interface_name, api_func, params, max_calls=5)
                time.sleep(2)  # 测试间隔
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        logging.info("\n📊 生成测试报告")
        logging.info("=" * 60)
        
        report = {
            'test_info': {
                'user_points': self.user_points,
                'test_time': self.start_time.isoformat(),
                'total_interfaces': len(self.test_results)
            },
            'results': dict(self.test_results)
        }
        
        # 保存JSON报告
        with open('tushare_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        logging.info(f"\n📋 测试摘要 (积分: {self.user_points})")
        logging.info("-" * 40)
        
        for interface_name, result in self.test_results.items():
            status = "✅" if result['actual_permission'] else "❌"
            expected = "✅" if result['expected_permission'] else "❌"
            
            logging.info(f"{status} {interface_name}: {result['description']}")
            logging.info(f"   预期权限: {expected} | 实际权限: {status}")
            
            if result['actual_permission']:
                logging.info(f"   成功调用: {result['success_count']}/{result['total_attempts']}")
                if 'calls_per_minute' in result:
                    logging.info(f"   实际频率: {result['calls_per_minute']:.1f} 次/分钟")
                    logging.info(f"   预期频率: {result['expected_rate_limit']} 次/分钟")
            
            if result['error_messages']:
                logging.info(f"   错误信息: {result['error_messages'][-1]}")  # 显示最后一个错误
            
            logging.info("")
        
        logging.info("📄 详细报告已保存到: tushare_test_report.json")
        logging.info("📄 日志文件已保存到: tushare_test_report.log")


def main():
    """主函数"""
    try:
        # 创建测试器
        tester = TushareAPITester(user_points=5000)
        
        # 运行综合测试
        tester.run_comprehensive_test()
        
        logging.info("\n🎉 测试完成！")
        
    except Exception as e:
        logging.error(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
