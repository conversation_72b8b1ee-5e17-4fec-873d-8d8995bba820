#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tushare接口测试脚本
测试limit_list_d和limit_step接口的实现
"""

import logging
import sys
import time
import pandas as pd
import tushare as ts
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('tushare_interfaces_test.log', encoding='utf-8')
    ]
)

def test_tushare_connection():
    """测试Tushare连接"""
    logging.info("🔧 测试1: Tushare连接验证")
    
    try:
        # 初始化Tushare（需要设置token）
        # 注意：实际使用时需要设置真实的token
        # ts.set_token('your_token_here')
        pro = ts.pro_api()
        
        # 测试基础连接 - 获取股票基本信息
        basic_info = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
        
        if not basic_info.empty:
            logging.info(f"✅ Tushare连接成功，获取到{len(basic_info)}只股票基本信息")
            return True, pro
        else:
            logging.error("❌ Tushare连接失败，未获取到数据")
            return False, None
            
    except Exception as e:
        logging.error(f"❌ Tushare连接测试失败: {str(e)}")
        return False, None

def test_limit_list_d_interface(pro, test_date='20250103'):
    """测试limit_list_d接口"""
    logging.info("🔧 测试2: limit_list_d接口验证")
    
    try:
        # 测试单日数据获取
        logging.info(f"测试获取{test_date}的涨跌停数据...")
        limit_data = pro.limit_list_d(trade_date=test_date)
        
        if not limit_data.empty:
            logging.info(f"✅ 成功获取{test_date}涨跌停数据: {len(limit_data)}条")
            
            # 显示数据结构
            logging.info(f"数据字段: {list(limit_data.columns)}")
            
            # 显示前几条数据
            if len(limit_data) > 0:
                logging.info("前3条数据示例:")
                for i, row in limit_data.head(3).iterrows():
                    logging.info(f"  {row.get('ts_code', 'N/A')} - {row.get('name', 'N/A')} - 涨停次数: {row.get('limit_times', 'N/A')}")
            
            return True, limit_data
        else:
            logging.warning(f"⚠️ {test_date}无涨跌停数据")
            return True, pd.DataFrame()
            
    except Exception as e:
        logging.error(f"❌ limit_list_d接口测试失败: {str(e)}")
        return False, None

def test_limit_step_interface(pro, test_date='20250103'):
    """测试limit_step接口"""
    logging.info("🔧 测试3: limit_step接口验证")
    
    try:
        # 测试单日数据获取
        logging.info(f"测试获取{test_date}的连板天梯数据...")
        step_data = pro.limit_step(trade_date=test_date)
        
        if not step_data.empty:
            logging.info(f"✅ 成功获取{test_date}连板天梯数据: {len(step_data)}条")
            
            # 显示数据结构
            logging.info(f"数据字段: {list(step_data.columns)}")
            
            # 统计连板分布
            if 'step' in step_data.columns:
                step_dist = step_data['step'].value_counts().sort_index()
                logging.info(f"连板分布统计:\n{step_dist}")
            
            # 显示前几条数据
            if len(step_data) > 0:
                logging.info("前3条数据示例:")
                for i, row in step_data.head(3).iterrows():
                    logging.info(f"  {row.get('ts_code', 'N/A')} - {row.get('name', 'N/A')} - 连板天数: {row.get('step', 'N/A')}")
            
            return True, step_data
        else:
            logging.warning(f"⚠️ {test_date}无连板天梯数据")
            return True, pd.DataFrame()
            
    except Exception as e:
        logging.error(f"❌ limit_step接口测试失败: {str(e)}")
        return False, None

def test_batch_data_fetching(pro):
    """测试批量数据获取（模拟实际使用场景）"""
    logging.info("🔧 测试4: 批量数据获取验证")
    
    try:
        # 测试3天的数据获取
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3)
        
        # 生成日期范围
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        limit_list_all = []
        limit_step_all = []
        
        for trade_date in date_range:
            trade_date_str = trade_date.strftime('%Y%m%d')
            
            try:
                # 获取涨跌停数据
                daily_limit = pro.limit_list_d(trade_date=trade_date_str)
                if not daily_limit.empty:
                    daily_limit['trade_date'] = trade_date_str
                    limit_list_all.append(daily_limit)
                    logging.info(f"获取{trade_date_str}涨跌停数据: {len(daily_limit)}条")
                
                # 控制请求频率
                time.sleep(0.2)  # 测试时使用较长间隔
                
                # 获取连板天梯数据
                daily_step = pro.limit_step(trade_date=trade_date_str)
                if not daily_step.empty:
                    daily_step['trade_date'] = trade_date_str
                    limit_step_all.append(daily_step)
                    logging.info(f"获取{trade_date_str}连板天梯数据: {len(daily_step)}条")
                
                # 控制请求频率
                time.sleep(0.2)
                
            except Exception as e:
                logging.warning(f"获取{trade_date_str}数据失败: {e}")
                continue
        
        # 合并数据
        if limit_list_all:
            limit_list_combined = pd.concat(limit_list_all, ignore_index=True)
            logging.info(f"✅ 合并涨跌停数据: {len(limit_list_combined)}条")
        else:
            limit_list_combined = pd.DataFrame()
            
        if limit_step_all:
            limit_step_combined = pd.concat(limit_step_all, ignore_index=True)
            logging.info(f"✅ 合并连板天梯数据: {len(limit_step_combined)}条")
        else:
            limit_step_combined = pd.DataFrame()
        
        return True, limit_list_combined, limit_step_combined
        
    except Exception as e:
        logging.error(f"❌ 批量数据获取测试失败: {str(e)}")
        return False, None, None

def test_data_processing_simulation():
    """测试数据处理模拟（不需要真实API）"""
    logging.info("🔧 测试5: 数据处理逻辑验证")
    
    try:
        # 模拟涨跌停数据
        mock_limit_list = pd.DataFrame({
            'ts_code': ['000001.SZ', '000002.SZ', '300001.SZ'],
            'trade_date': ['20250103', '20250103', '20250103'],
            'name': ['平安银行', '万科A', '特锐德'],
            'limit_times': [1, 2, 1],
            'open_times': [0, 1, 0]
        })
        
        # 模拟连板天梯数据
        mock_limit_step = pd.DataFrame({
            'ts_code': ['000001.SZ', '000002.SZ', '300001.SZ'],
            'trade_date': ['20250103', '20250103', '20250103'],
            'name': ['平安银行', '万科A', '特锐德'],
            'step': [1, 2, 1],
            'step_up_num': [10, 5, 8],
            'step_down_num': [2, 3, 1]
        })
        
        # 模拟主数据框
        mock_main_df = pd.DataFrame({
            'ts_code': ['000001.SZ', '000002.SZ', '300001.SZ', '600000.SH'],
            'trade_date': ['20250103', '20250103', '20250103', '20250103'],
            'close': [10.5, 25.8, 45.2, 8.9],
            'pct_chg': [9.95, 10.0, 9.87, 2.5]
        })
        
        # 转换日期格式
        mock_limit_list['trade_date'] = pd.to_datetime(mock_limit_list['trade_date'])
        mock_limit_step['trade_date'] = pd.to_datetime(mock_limit_step['trade_date'])
        mock_main_df['trade_date'] = pd.to_datetime(mock_main_df['trade_date'])
        
        # 模拟数据合并过程
        logging.info("模拟涨跌停数据合并...")
        df_with_limit = mock_main_df.merge(
            mock_limit_list[['ts_code', 'trade_date', 'limit_times', 'open_times']],
            on=['ts_code', 'trade_date'], 
            how='left'
        )
        df_with_limit['limit_times'] = df_with_limit['limit_times'].fillna(0)
        df_with_limit['open_times'] = df_with_limit['open_times'].fillna(0)
        
        logging.info("模拟连板天梯数据合并...")
        df_final = df_with_limit.merge(
            mock_limit_step[['ts_code', 'trade_date', 'step', 'step_up_num', 'step_down_num']],
            on=['ts_code', 'trade_date'],
            how='left'
        )
        df_final['step'] = df_final['step'].fillna(0)
        df_final['step_up_num'] = df_final['step_up_num'].fillna(0)
        df_final['step_down_num'] = df_final['step_down_num'].fillna(0)
        
        # 验证合并结果
        logging.info("合并结果验证:")
        logging.info(f"最终数据形状: {df_final.shape}")
        logging.info(f"字段列表: {list(df_final.columns)}")
        
        # 检查数据完整性
        for col in ['limit_times', 'open_times', 'step', 'step_up_num', 'step_down_num']:
            if col in df_final.columns:
                non_null_count = df_final[col].notna().sum()
                logging.info(f"  {col}: {non_null_count}/{len(df_final)} 非空")
            else:
                logging.warning(f"  缺少字段: {col}")
        
        logging.info("✅ 数据处理逻辑验证通过")
        return True
        
    except Exception as e:
        logging.error(f"❌ 数据处理逻辑测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logging.info("🚀 开始Tushare接口测试")
    logging.info("=" * 60)
    
    test_results = []
    
    # 测试1: Tushare连接
    connection_success, pro = test_tushare_connection()
    test_results.append(("Tushare连接", connection_success))
    
    if connection_success and pro is not None:
        # 测试2: limit_list_d接口
        limit_list_success, _ = test_limit_list_d_interface(pro)
        test_results.append(("limit_list_d接口", limit_list_success))
        
        # 测试3: limit_step接口
        limit_step_success, _ = test_limit_step_interface(pro)
        test_results.append(("limit_step接口", limit_step_success))
        
        # 测试4: 批量数据获取
        batch_success, _, _ = test_batch_data_fetching(pro)
        test_results.append(("批量数据获取", batch_success))
    else:
        logging.warning("⚠️ 跳过API相关测试（连接失败）")
        test_results.extend([
            ("limit_list_d接口", False),
            ("limit_step接口", False),
            ("批量数据获取", False)
        ])
    
    # 测试5: 数据处理逻辑（不依赖API）
    processing_success = test_data_processing_simulation()
    test_results.append(("数据处理逻辑", processing_success))
    
    # 汇总结果
    logging.info("\n" + "=" * 60)
    logging.info("📊 测试结果汇总:")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"  {test_name}: {status}")
    
    logging.info(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed >= 3:  # 至少数据处理逻辑要通过
        logging.info("🎉 Tushare接口实现基本正确！")
        logging.info("✅ 可以在P.pull.py中使用这些接口")
        if passed < total:
            logging.info("⚠️ 部分API测试失败可能是由于网络或token配置问题")
        return True
    else:
        logging.error("⚠️ 接口实现存在问题，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
