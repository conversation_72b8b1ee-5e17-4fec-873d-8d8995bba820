#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AdEMAMix优化器集成到P.pull.py中的功能
"""

import sys
import os
import numpy as np
import tensorflow as tf
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_ademamix_import():
    """测试AdEMAMix优化器导入"""
    try:
        # 导入P.pull.py中的AdEMAMix
        sys.path.append('.')
        from P.pull import AdEMAMix, Config
        
        print("✓ AdEMAMix优化器导入成功")
        print(f"✓ Config.USE_ADEMAMIX = {Config.USE_ADEMAMIX}")
        
        return AdEMAMix, Config
    except Exception as e:
        print(f"✗ AdEMAMix优化器导入失败: {str(e)}")
        return None, None

def test_ademamix_creation():
    """测试AdEMAMix优化器创建"""
    try:
        AdEMAMix, Config = test_ademamix_import()
        if AdEMAMix is None:
            return False
        
        # 创建AdEMAMix优化器实例
        optimizer = AdEMAMix(
            learning_rate=1e-4,
            beta_1=0.9,
            beta_2=0.999,
            alpha=0.5,
            epsilon=1e-8,
            weight_decay=0.01
        )
        
        print("✓ AdEMAMix优化器创建成功")
        print(f"  类型: {type(optimizer)}")
        print(f"  名称: {optimizer.name}")
        
        return True
    except Exception as e:
        print(f"✗ AdEMAMix优化器创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ademamix_training():
    """测试AdEMAMix优化器训练"""
    try:
        AdEMAMix, Config = test_ademamix_import()
        if AdEMAMix is None:
            return False
        
        # 创建简单的测试数据
        X = np.random.randn(1000, 10).astype(np.float32)
        y = np.random.randn(1000, 1).astype(np.float32)
        
        # 创建简单模型
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(32, activation='relu', input_shape=(10,)),
            tf.keras.layers.Dense(16, activation='relu'),
            tf.keras.layers.Dense(1)
        ])
        
        # 创建AdEMAMix优化器
        optimizer = AdEMAMix(
            learning_rate=1e-3,
            beta_1=0.9,
            beta_2=0.999,
            alpha=0.5,
            epsilon=1e-8,
            weight_decay=0.01
        )
        
        # 编译模型
        model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])
        
        print("✓ 模型编译成功")
        
        # 训练模型
        history = model.fit(X, y, epochs=3, batch_size=32, verbose=0)
        
        print("✓ 模型训练成功")
        print(f"  初始损失: {history.history['loss'][0]:.6f}")
        print(f"  最终损失: {history.history['loss'][-1]:.6f}")
        print(f"  损失改善: {(history.history['loss'][0] - history.history['loss'][-1]):.6f}")
        
        return True
    except Exception as e:
        print(f"✗ AdEMAMix优化器训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config_integration():
    """测试配置集成"""
    try:
        from P.pull import Config, get_optimized_compilation_config
        
        print("✓ 配置导入成功")
        print(f"  USE_ADEMAMIX: {Config.USE_ADEMAMIX}")
        print(f"  DEFAULT_LEARNING_RATE: {Config.DEFAULT_LEARNING_RATE}")
        print(f"  GRADIENT_CLIP_NORM: {Config.GRADIENT_CLIP_NORM}")
        
        # 测试编译配置
        compile_config = get_optimized_compilation_config('首板', Config.DEFAULT_LEARNING_RATE)
        optimizer, lr_schedule, normalize_func, calc_class_weights = compile_config
        
        print("✓ 编译配置获取成功")
        print(f"  优化器类型: {type(optimizer)}")
        print(f"  优化器名称: {optimizer.name}")
        
        return True
    except Exception as e:
        print(f"✗ 配置集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_serialization():
    """测试序列化和反序列化"""
    try:
        AdEMAMix, Config = test_ademamix_import()
        if AdEMAMix is None:
            return False
        
        # 创建优化器
        optimizer = AdEMAMix(
            learning_rate=1e-4,
            beta_1=0.9,
            beta_2=0.999,
            alpha=0.5,
            epsilon=1e-8,
            weight_decay=0.01
        )
        
        # 测试序列化
        config = optimizer.get_config()
        print("✓ 优化器序列化成功")
        print(f"  配置键: {list(config.keys())}")
        
        # 测试反序列化
        optimizer_restored = AdEMAMix.from_config(config)
        print("✓ 优化器反序列化成功")
        print(f"  恢复的优化器类型: {type(optimizer_restored)}")
        
        return True
    except Exception as e:
        print(f"✗ 序列化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("AdEMAMix优化器集成测试")
    print("="*60)
    
    tests = [
        ("导入测试", lambda: test_ademamix_import()[0] is not None),
        ("创建测试", test_ademamix_creation),
        ("训练测试", test_ademamix_training),
        ("配置集成测试", test_config_integration),
        ("序列化测试", test_serialization)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results[test_name] = result
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"{test_name}: ✗ 异常 - {str(e)}")
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！AdEMAMix优化器集成成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
