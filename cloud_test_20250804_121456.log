2025-08-04 12:15:43,333 - INFO - 开始完整的云服务器优化器测试流水线
2025-08-04 12:15:43,335 - INFO - 本地文件检查完成
2025-08-04 12:15:43,335 - INFO - 开始上传文件到云服务器...
2025-08-04 12:15:48,843 - INFO - 上传文件: optimizer_test_framework.py
2025-08-04 12:15:55,298 - INFO - 文件 optimizer_test_framework.py 上传成功
2025-08-04 12:15:55,298 - INFO - 上传文件: financial_optimizer_test.py
2025-08-04 12:16:03,304 - INFO - 文件 financial_optimizer_test.py 上传成功
2025-08-04 12:16:03,304 - INFO - 上传文件: cloud_server_test.py
2025-08-04 12:16:09,911 - INFO - 文件 cloud_server_test.py 上传成功
2025-08-04 12:16:09,911 - INFO - 所有文件上传完成
2025-08-04 12:16:09,912 - INFO - 跳过依赖安装（环境已准备好）
2025-08-04 12:16:09,912 - INFO - 在云服务器上运行优化器测试...
2025-08-04 12:16:14,598 - ERROR - 运行测试时出错: Command 'ssh -i /Users/<USER>/Downloads/P.pem ubuntu@124.220.225.145 'cd /home/<USER>/optimizer_test && echo '{
  "data_config": {
    "n_samples": 5000,
    "n_features": 20,
    "sequence_length": 60
  },
  "test_config": {
    "learning_rate": 0.0001,
    "batch_size": 32,
    "epochs": 30,
    "loss": "mse",
    "metrics": [
      "mae",
      "mse"
    ]
  },
  "optimizer_list": [
    "adam",
    "adamw",
    "lion",
    "ademamix",
    "fractional"
  ]
}' > test_config.json'' returned non-zero exit status 127.
