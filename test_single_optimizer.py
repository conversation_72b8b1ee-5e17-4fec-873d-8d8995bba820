#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个优化器的脚本
"""

import numpy as np
import tensorflow as tf
from optimizer_test_framework import OptimizerTestFramework

def test_single_optimizer(optimizer_name):
    """测试单个优化器"""
    print(f"测试优化器: {optimizer_name}")
    
    # 创建简单的测试数据
    X = np.random.randn(1000, 10).astype(np.float32)
    y = np.random.randn(1000, 1).astype(np.float32)
    
    # 创建简单模型
    def create_model():
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(32, activation='relu', input_shape=(10,)),
            tf.keras.layers.Dense(16, activation='relu'),
            tf.keras.layers.Dense(1)
        ])
        return model
    
    # 创建测试框架
    test_config = {
        'learning_rate': 1e-4,
        'batch_size': 32,
        'epochs': 5,
        'loss': 'mse',
        'metrics': ['mae']
    }
    
    framework = OptimizerTestFramework(test_config)
    
    try:
        # 创建优化器
        optimizer = framework.get_optimizer_by_name(optimizer_name, 1e-4)
        print(f"✓ 优化器 {optimizer_name} 创建成功: {type(optimizer)}")
        
        # 创建模型
        model = create_model()
        
        # 编译模型
        model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])
        print(f"✓ 模型编译成功")
        
        # 训练模型
        history = model.fit(X, y, epochs=2, batch_size=32, verbose=0)
        print(f"✓ 训练成功，最终损失: {history.history['loss'][-1]:.6f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 优化器 {optimizer_name} 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    optimizers = ['adam', 'adamw', 'lion', 'ademamix', 'fractional', 'lookahead_adam']
    
    results = {}
    for opt_name in optimizers:
        print(f"\n{'='*50}")
        success = test_single_optimizer(opt_name)
        results[opt_name] = success
        print(f"{'='*50}")
    
    print(f"\n\n测试结果总结:")
    print("-" * 30)
    for opt_name, success in results.items():
        status = "✓ 成功" if success else "✗ 失败"
        print(f"{opt_name:15}: {status}")

if __name__ == "__main__":
    main()
