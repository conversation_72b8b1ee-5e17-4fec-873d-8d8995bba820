#!/bin/bash
echo "🔧 在云服务器上测试修复效果..."
echo "=============================="

# 检查Python环境
echo "🐍 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查必要的包
echo "📦 检查Python包..."
python3 -c "import pandas, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 缺少必要的包，尝试安装..."
    pip3 install pandas numpy
fi

echo ""
echo "🚀 开始核心修复测试..."
echo "===================="
python3 test_core_fixes.py

echo ""
echo "📊 测试完成！"
echo "============"

# 检查是否有config.py和tushare
if [ -f "config.py" ]; then
    echo "🔍 检查Tushare配置..."
    python3 -c "
import sys
try:
    import tushare as ts
    from config import Config
    ts.set_token(Config.TUSHARE_TOKEN)
    pro = ts.pro_api()
    
    # 测试基础调用
    data = pro.daily(ts_code='000001.SZ', start_date='20241201', end_date='20241201')
    print('✅ Tushare配置正常')
    
    # 测试修复后的权限检查
    from P import check_tushare_permissions
    permissions = check_tushare_permissions()
    print('✅ 权限检查功能正常')
    
except ImportError as e:
    print(f'⚠️ 导入失败: {e}')
except Exception as e:
    print(f'⚠️ 测试失败: {e}')
"
else
    echo "⚠️ config.py 不存在，跳过Tushare测试"
fi

echo ""
echo "🎯 修复验证完成！"
echo "================"
echo "如果所有测试通过，说明修复成功："
echo "1. ✅ limit_step接口已移除，使用替代方案"
echo "2. ✅ astype错误已修复"
echo "3. ✅ 序列数据诊断功能正常"
echo "4. ✅ 特征计算修复完成"
