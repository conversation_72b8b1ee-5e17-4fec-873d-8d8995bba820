{"comprehensive_scores": {"adam": {"total_score": 0.5960867897744084, "performance_score": 0.7273047149894529, "stability_score": 0.8143322475570033, "efficiency_score": 0.5055248618784559, "memory_score": 0.0, "convergence_score": 0.7368421052631579, "combined_metrics": {"final_val_loss": 0.07006899999999999, "convergence_speed": 69.5, "training_time": 55.80499999999999, "stability": 0.0007149999999999999, "memory_mb": 171.44}}, "adamw": {"total_score": 0.4259132546076592, "performance_score": 0.0, "stability_score": 1.0, "efficiency_score": 0.0, "memory_score": 0.8394216973843948, "convergence_score": 1.0, "combined_metrics": {"final_val_loss": 0.0711025, "convergence_speed": 67.0, "training_time": 56.72, "stability": 0.000658, "memory_mb": 57.349999999999994}}, "lion": {"total_score": 0.46249599247744616, "performance_score": 0.7005629838142169, "stability_score": 0.0, "efficiency_score": 0.2624309392265159, "memory_score": 0.9934885774197109, "convergence_score": 0.3157894736842105, "combined_metrics": {"final_val_loss": 0.070107, "convergence_speed": 73.5, "training_time": 56.245000000000005, "stability": 0.0009649999999999999, "memory_mb": 36.41}}, "ademamix": {"total_score": 0.8868976231217816, "performance_score": 1.0, "stability_score": 0.6921824104234525, "efficiency_score": 1.0, "memory_score": 0.9695397858955966, "convergence_score": 0.3684210526315789, "combined_metrics": {"final_val_loss": 0.06968150000000001, "convergence_speed": 73.0, "training_time": 54.91, "stability": 0.0007525, "memory_mb": 39.665}}, "fractional": {"total_score": 0.716175313944442, "performance_score": 0.8395496129486324, "stability_score": 0.353420195439739, "efficiency_score": 0.9198895027624293, "memory_score": 1.0, "convergence_score": 0.0, "combined_metrics": {"final_val_loss": 0.0699095, "convergence_speed": 76.5, "training_time": 55.055, "stability": 0.0008565000000000001, "memory_mb": 35.525}}}, "weights": {"performance": 0.35, "stability": 0.25, "efficiency": 0.2, "memory": 0.15, "convergence": 0.05}, "financial_analysis": {"adam": {"risk_adjusted_performance": -97.99723080796072, "efficiency_ratio": -0.0012556043363039864, "memory_efficiency": -0.0004087085860704206, "stability_score": 1398.5818380162516, "final_val_loss": 0.07006899999999999}, "adamw": {"risk_adjusted_performance": -108.05686843664989, "efficiency_ratio": -0.001253570169031458, "memory_efficiency": -0.00123979947668007, "stability_score": 1519.733742648288, "final_val_loss": 0.0711025}, "lion": {"risk_adjusted_performance": -72.64898809338764, "efficiency_ratio": -0.0012464574626639775, "memory_efficiency": -0.0019254875029042881, "stability_score": 1036.258691619776, "final_val_loss": 0.070107}, "ademamix": {"risk_adjusted_performance": -92.59876945156876, "efficiency_ratio": -0.0012690129300183916, "memory_efficiency": -0.0017567502831824658, "stability_score": 1328.8859948704999, "final_val_loss": 0.06968150000000001}, "fractional": {"risk_adjusted_performance": -81.62134709460484, "efficiency_ratio": -0.0012698120059449983, "memory_efficiency": -0.0019678958474404233, "stability_score": 1167.528692017606, "final_val_loss": 0.0699095}}, "final_recommendation": {"primary_recommendation": "ademamix", "reason": "综合评分最高且在关键金融指标中表现优秀", "comprehensive_ranking": ["ademamix", "fractional", "adam"], "financial_best": {"best_risk_adjusted": "lion", "best_efficiency": "lion", "best_memory_efficiency": "adam", "best_stability": "adamw", "best_performance": "ademamix"}, "detailed_scores": {"total_score": 0.8868976231217816, "performance_score": 1.0, "stability_score": 0.6921824104234525, "efficiency_score": 1.0, "memory_score": 0.9695397858955966, "convergence_score": 0.3684210526315789, "combined_metrics": {"final_val_loss": 0.06968150000000001, "convergence_speed": 73.0, "training_time": 54.91, "stability": 0.0007525, "memory_mb": 39.665}}}}