#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P.pull.py AUC标准化修复验证测试
验证自定义FixedAUC类是否已完全删除，确保只使用标准AUC
"""

import logging
import sys
import os
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('auc_fix_test.log', encoding='utf-8')
    ]
)

def test_fixed_auc_removal():
    """测试FixedAUC类是否已完全删除"""
    logging.info("🔧 测试1: 验证FixedAUC类定义是否已删除")
    
    try:
        with open('P.pull.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有FixedAUC类定义
        class_pattern = r'class\s+FixedAUC\s*\('
        class_matches = re.findall(class_pattern, content)
        
        if class_matches:
            logging.error(f"❌ 仍然存在FixedAUC类定义: {len(class_matches)}个")
            return False
        
        logging.info("✅ FixedAUC类定义已完全删除")
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def test_fixed_auc_references():
    """测试FixedAUC引用是否已清理"""
    logging.info("🔧 测试2: 验证FixedAUC引用是否已清理")
    
    try:
        with open('P.pull.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找非注释行中的FixedAUC引用
        active_references = []
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            # 跳过注释行
            if stripped.startswith('#'):
                continue
            # 跳过空行
            if not stripped:
                continue
            
            if 'FixedAUC' in line and not line.strip().startswith('#'):
                active_references.append((i, line.strip()))
        
        if active_references:
            logging.error(f"❌ 仍然存在{len(active_references)}个活跃的FixedAUC引用:")
            for line_num, line_content in active_references:
                logging.error(f"  第{line_num}行: {line_content}")
            return False
        
        logging.info("✅ 所有活跃的FixedAUC引用已清理")
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def test_standard_auc_usage():
    """测试标准AUC的使用"""
    logging.info("🔧 测试3: 验证标准AUC的使用")
    
    try:
        with open('P.pull.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查tf.keras.metrics.AUC的使用
        keras_auc_pattern = r'tf\.keras\.metrics\.AUC\s*\('
        keras_auc_matches = re.findall(keras_auc_pattern, content)
        
        # 检查sklearn.metrics.auc的使用
        sklearn_auc_pattern = r'from sklearn\.metrics import.*auc'
        sklearn_auc_matches = re.findall(sklearn_auc_pattern, content)
        
        logging.info(f"✅ 发现{len(keras_auc_matches)}处tf.keras.metrics.AUC使用")
        logging.info(f"✅ 发现{len(sklearn_auc_matches)}处sklearn.metrics.auc导入")
        
        if len(keras_auc_matches) == 0:
            logging.warning("⚠️ 未发现tf.keras.metrics.AUC的使用")
            return False
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def test_import_compatibility():
    """测试导入兼容性"""
    logging.info("🔧 测试4: 验证导入兼容性")
    
    try:
        # 尝试导入TensorFlow和相关模块
        import tensorflow as tf
        from tensorflow.keras.metrics import AUC, BinaryAccuracy
        from sklearn.metrics import auc, roc_curve
        
        logging.info("✅ TensorFlow AUC导入成功")
        logging.info("✅ sklearn AUC导入成功")
        
        # 测试创建标准AUC实例
        standard_auc = AUC(name='test_auc')
        logging.info("✅ 标准AUC实例创建成功")
        
        return True
        
    except ImportError as e:
        logging.error(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def test_auc_calculation():
    """测试AUC计算功能"""
    logging.info("🔧 测试5: 验证AUC计算功能")
    
    try:
        import tensorflow as tf
        import numpy as np
        from sklearn.metrics import roc_auc_score
        
        # 创建测试数据
        y_true = np.array([0, 0, 1, 1, 0, 1, 0, 1])
        y_pred = np.array([0.1, 0.4, 0.35, 0.8, 0.2, 0.9, 0.3, 0.7])
        
        # 使用sklearn计算AUC
        sklearn_auc = roc_auc_score(y_true, y_pred)
        
        # 使用TensorFlow计算AUC
        tf_auc_metric = tf.keras.metrics.AUC()
        tf_auc_metric.update_state(y_true, y_pred)
        tf_auc = tf_auc_metric.result().numpy()
        
        # 比较结果
        auc_diff = abs(sklearn_auc - tf_auc)
        
        logging.info(f"✅ sklearn AUC: {sklearn_auc:.4f}")
        logging.info(f"✅ TensorFlow AUC: {tf_auc:.4f}")
        logging.info(f"✅ 差异: {auc_diff:.6f}")
        
        if auc_diff < 0.01:  # 允许小的数值差异
            logging.info("✅ AUC计算结果一致")
            return True
        else:
            logging.warning(f"⚠️ AUC计算结果差异较大: {auc_diff}")
            return False
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def test_code_syntax():
    """测试代码语法正确性"""
    logging.info("🔧 测试6: 验证代码语法正确性")
    
    try:
        # 尝试编译P.pull.py
        with open('P.pull.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'P.pull.py', 'exec')
        logging.info("✅ 代码语法检查通过")
        return True
        
    except SyntaxError as e:
        logging.error(f"❌ 语法错误: {str(e)}")
        logging.error(f"  第{e.lineno}行: {e.text}")
        return False
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logging.info("🚀 开始P.pull.py AUC标准化修复验证测试")
    logging.info("=" * 60)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("FixedAUC类定义删除", test_fixed_auc_removal),
        ("FixedAUC引用清理", test_fixed_auc_references),
        ("标准AUC使用验证", test_standard_auc_usage),
        ("导入兼容性测试", test_import_compatibility),
        ("AUC计算功能测试", test_auc_calculation),
        ("代码语法检查", test_code_syntax)
    ]
    
    for test_name, test_func in tests:
        logging.info(f"\n📋 执行测试: {test_name}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                logging.info(f"✅ {test_name} - 通过")
            else:
                logging.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logging.error(f"❌ {test_name} - 异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 汇总结果
    logging.info("\n" + "=" * 60)
    logging.info("📊 测试结果汇总:")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"  {test_name}: {status}")
    
    logging.info(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logging.info("🎉 所有测试通过！AUC标准化修复成功！")
        logging.info("✅ P.pull.py现在完全使用标准AUC，无自定义AUC污染")
        return True
    else:
        logging.error(f"⚠️ {total - passed}个测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
