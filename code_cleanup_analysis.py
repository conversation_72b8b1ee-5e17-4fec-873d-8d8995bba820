#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码清理分析脚本
分析P.pull.py中的重复代码、无效函数和冲突问题
"""

import ast
import re
from collections import defaultdict
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def analyze_duplicate_functions(file_path):
    """分析重复的函数定义"""
    print("🔍 分析重复函数定义...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找函数定义
    function_pattern = r'^def\s+(\w+)\s*\('
    class_pattern = r'^class\s+(\w+)\s*[\(:]'
    
    functions = defaultdict(list)
    classes = defaultdict(list)
    
    lines = content.split('\n')
    for i, line in enumerate(lines, 1):
        # 查找函数定义
        func_match = re.match(function_pattern, line.strip())
        if func_match:
            func_name = func_match.group(1)
            functions[func_name].append(i)
        
        # 查找类定义
        class_match = re.match(class_pattern, line.strip())
        if class_match:
            class_name = class_match.group(1)
            classes[class_name].append(i)
    
    # 报告重复定义
    print("\n📋 重复函数定义:")
    duplicates = []
    for func_name, line_numbers in functions.items():
        if len(line_numbers) > 1:
            print(f"  ❌ {func_name}: 行 {line_numbers}")
            duplicates.append(('function', func_name, line_numbers))
    
    print("\n📋 重复类定义:")
    for class_name, line_numbers in classes.items():
        if len(line_numbers) > 1:
            print(f"  ❌ {class_name}: 行 {line_numbers}")
            duplicates.append(('class', class_name, line_numbers))
    
    return duplicates

def analyze_unused_functions(file_path):
    """分析未使用的函数"""
    print("\n🔍 分析未使用的函数...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有函数定义
    function_pattern = r'^def\s+(\w+)\s*\('
    defined_functions = set()
    
    lines = content.split('\n')
    for line in lines:
        func_match = re.match(function_pattern, line.strip())
        if func_match:
            func_name = func_match.group(1)
            # 排除特殊方法
            if not func_name.startswith('_'):
                defined_functions.add(func_name)
    
    # 查找函数调用
    used_functions = set()
    for func_name in defined_functions:
        # 查找函数调用模式
        call_patterns = [
            rf'\b{func_name}\s*\(',  # 直接调用
            rf'\.{func_name}\s*\(',  # 方法调用
        ]
        
        for pattern in call_patterns:
            if re.search(pattern, content):
                used_functions.add(func_name)
                break
    
    # 找出未使用的函数
    unused_functions = defined_functions - used_functions
    
    print(f"\n📊 函数使用统计:")
    print(f"  总定义函数: {len(defined_functions)}")
    print(f"  已使用函数: {len(used_functions)}")
    print(f"  未使用函数: {len(unused_functions)}")
    
    if unused_functions:
        print(f"\n📋 可能未使用的函数:")
        for func in sorted(unused_functions):
            print(f"  ⚠️ {func}")
    
    return unused_functions

def analyze_conflicting_code(file_path):
    """分析冲突的代码段"""
    print("\n🔍 分析代码冲突...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    conflicts = []
    
    # 检查Lookahead类冲突
    lookahead_pattern = r'class\s+Lookahead\s*\('
    lookahead_matches = []
    lines = content.split('\n')
    
    for i, line in enumerate(lines, 1):
        if re.search(lookahead_pattern, line):
            lookahead_matches.append(i)
    
    if len(lookahead_matches) > 1:
        conflicts.append(('Lookahead类重复定义', lookahead_matches))
        print(f"  ❌ Lookahead类重复定义: 行 {lookahead_matches}")
    
    # 检查市场类型函数冲突
    market_functions = []
    market_patterns = [
        (r'def\s+get_market_type\s*\(', 'get_market_type'),
        (r'def\s+get_market_type_from_code\s*\(', 'get_market_type_from_code'),
        (r'def\s+vectorized_get_market_type\s*\(', 'vectorized_get_market_type')
    ]
    
    for pattern, name in market_patterns:
        for i, line in enumerate(lines, 1):
            if re.search(pattern, line):
                market_functions.append((name, i))
    
    if len(market_functions) > 1:
        conflicts.append(('市场类型函数重复', market_functions))
        print(f"  ❌ 市场类型函数重复:")
        for name, line_num in market_functions:
            print(f"    - {name}: 行 {line_num}")
    
    return conflicts

def analyze_invalid_files():
    """分析无效的文件"""
    print("\n🔍 分析无效文件...")
    
    import os
    import glob
    
    # 查找.md分析报告文件
    md_files = glob.glob("*.md")
    analysis_files = [f for f in md_files if any(keyword in f.lower() for keyword in 
                     ['分析', '报告', '修复', '优化', '问题'])]
    
    if analysis_files:
        print(f"\n📋 发现分析报告文件 (可能需要清理):")
        for file in analysis_files:
            print(f"  📄 {file}")
    
    return analysis_files

def generate_cleanup_plan(duplicates, unused_functions, conflicts, invalid_files):
    """生成清理计划"""
    print("\n" + "="*60)
    print("🎯 代码清理计划")
    print("="*60)
    
    cleanup_plan = []
    
    # 1. 重复代码清理
    if duplicates:
        print("\n1. 🔧 重复代码清理:")
        for item_type, name, line_numbers in duplicates:
            if name == 'Lookahead':
                print(f"   ✅ 删除重复的{name}类定义 (保留第{min(line_numbers)}行，删除其他)")
                cleanup_plan.append(('remove_duplicate_class', name, line_numbers[1:]))
            else:
                print(f"   ⚠️ 检查重复的{item_type} {name} (行: {line_numbers})")
                cleanup_plan.append(('review_duplicate', item_type, name, line_numbers))
    
    # 2. 冲突解决
    if conflicts:
        print("\n2. 🔧 冲突解决:")
        for conflict_type, details in conflicts:
            print(f"   ✅ 解决{conflict_type}")
            if 'Lookahead' in conflict_type:
                cleanup_plan.append(('fix_lookahead_conflict', details))
            elif '市场类型' in conflict_type:
                cleanup_plan.append(('unify_market_functions', details))
    
    # 3. 无效文件清理
    if invalid_files:
        print("\n3. 🗑️ 无效文件清理:")
        for file in invalid_files:
            print(f"   ✅ 删除分析报告文件: {file}")
            cleanup_plan.append(('remove_file', file))
    
    # 4. 未使用函数清理
    if unused_functions:
        print(f"\n4. 🧹 未使用函数清理 ({len(unused_functions)}个):")
        # 只显示前10个
        for func in sorted(list(unused_functions)[:10]):
            print(f"   ⚠️ 检查是否可删除: {func}")
        if len(unused_functions) > 10:
            print(f"   ... 还有{len(unused_functions) - 10}个函数")
        cleanup_plan.append(('review_unused_functions', unused_functions))
    
    print(f"\n📊 清理统计:")
    print(f"   重复定义: {len(duplicates)}")
    print(f"   代码冲突: {len(conflicts)}")
    print(f"   无效文件: {len(invalid_files)}")
    print(f"   未使用函数: {len(unused_functions)}")
    
    return cleanup_plan

def main():
    """主分析函数"""
    print("🚀 开始代码清理分析")
    print("="*60)
    
    file_path = "P.pull.py"
    
    try:
        # 1. 分析重复函数
        duplicates = analyze_duplicate_functions(file_path)
        
        # 2. 分析未使用函数
        unused_functions = analyze_unused_functions(file_path)
        
        # 3. 分析代码冲突
        conflicts = analyze_conflicting_code(file_path)
        
        # 4. 分析无效文件
        invalid_files = analyze_invalid_files()
        
        # 5. 生成清理计划
        cleanup_plan = generate_cleanup_plan(duplicates, unused_functions, conflicts, invalid_files)
        
        print(f"\n🎉 分析完成！发现 {len(cleanup_plan)} 项清理任务")
        
        return cleanup_plan
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        return []

if __name__ == "__main__":
    cleanup_plan = main()
