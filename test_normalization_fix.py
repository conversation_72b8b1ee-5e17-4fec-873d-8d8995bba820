#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P.pull.py 标准化和反标准化修复验证测试
验证涨跌幅合理性检查是否根据A股板块规则正确工作
"""

import logging
import sys
import numpy as np

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('normalization_fix_test.log', encoding='utf-8')
    ]
)

def test_market_type_detection():
    """测试市场类型检测功能"""
    logging.info("🔧 测试1: 市场类型检测功能")
    
    try:
        # 模拟get_market_type_from_code函数
        def get_market_type_from_code(ts_code):
            """根据股票代码获取市场类型（单个代码）"""
            if ts_code.endswith('.SZ'):
                code_num = ts_code[:6]
                if code_num.startswith('00'):
                    return 'MAIN'  # 主板
                elif code_num.startswith('30'):
                    return 'CHINEXT'  # 创业板
                else:
                    return 'MAIN'
            elif ts_code.endswith('.SH'):
                code_num = ts_code[:6]
                if code_num.startswith('68'):
                    return 'STAR'  # 科创板
                else:
                    return 'MAIN'  # 主板
            elif ts_code.endswith('.BJ'):
                return 'BSE'  # 北交所
            else:
                return 'MAIN'  # 默认主板
        
        # 测试用例
        test_cases = [
            ('000001.SZ', 'MAIN', '平安银行-主板'),
            ('300001.SZ', 'CHINEXT', '特锐德-创业板'),
            ('600000.SH', 'MAIN', '浦发银行-主板'),
            ('688001.SH', 'STAR', '华兴源创-科创板'),
            ('430001.BJ', 'BSE', '北交所股票'),
        ]
        
        all_passed = True
        for ts_code, expected, description in test_cases:
            result = get_market_type_from_code(ts_code)
            if result == expected:
                logging.info(f"✅ {ts_code} -> {result} ({description})")
            else:
                logging.error(f"❌ {ts_code} -> {result}, 期望: {expected} ({description})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def test_max_return_calculation():
    """测试最大涨跌幅计算"""
    logging.info("🔧 测试2: 最大涨跌幅计算")
    
    try:
        # 模拟get_max_return函数
        def get_market_type_from_code(ts_code):
            if ts_code.endswith('.SZ'):
                code_num = ts_code[:6]
                if code_num.startswith('00'):
                    return 'MAIN'
                elif code_num.startswith('30'):
                    return 'CHINEXT'
                else:
                    return 'MAIN'
            elif ts_code.endswith('.SH'):
                code_num = ts_code[:6]
                if code_num.startswith('68'):
                    return 'STAR'
                else:
                    return 'MAIN'
            elif ts_code.endswith('.BJ'):
                return 'BSE'
            else:
                return 'MAIN'
        
        def get_max_return(ts_code):
            """根据股票代码确定最大涨幅"""
            market_type = get_market_type_from_code(ts_code)
            
            # 涨跌幅限制对应表
            limit_dict = {
                'MAIN': 9.8,      # 主板（含原中小板）
                'CHINEXT': 19.8,  # 创业板
                'STAR': 19.8,     # 科创板
                'BSE': 29.8       # 北交所
            }
            
            return limit_dict.get(market_type, 9.8)
        
        # 测试用例
        test_cases = [
            ('000001.SZ', 9.8, '主板股票'),
            ('300001.SZ', 19.8, '创业板股票'),
            ('600000.SH', 9.8, '主板股票'),
            ('688001.SH', 19.8, '科创板股票'),
            ('430001.BJ', 29.8, '北交所股票'),
        ]
        
        all_passed = True
        for ts_code, expected, description in test_cases:
            result = get_max_return(ts_code)
            if abs(result - expected) < 0.01:
                logging.info(f"✅ {ts_code} -> ±{result}% ({description})")
            else:
                logging.error(f"❌ {ts_code} -> ±{result}%, 期望: ±{expected}% ({description})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def test_denormalization_with_limits():
    """测试反标准化函数的涨跌幅限制"""
    logging.info("🔧 测试3: 反标准化涨跌幅限制")
    
    try:
        # 模拟完整的反标准化函数
        def get_market_type_from_code(ts_code):
            if ts_code.endswith('.SZ'):
                code_num = ts_code[:6]
                if code_num.startswith('00'):
                    return 'MAIN'
                elif code_num.startswith('30'):
                    return 'CHINEXT'
                else:
                    return 'MAIN'
            elif ts_code.endswith('.SH'):
                code_num = ts_code[:6]
                if code_num.startswith('68'):
                    return 'STAR'
                else:
                    return 'MAIN'
            elif ts_code.endswith('.BJ'):
                return 'BSE'
            else:
                return 'MAIN'
        
        def get_max_return(ts_code):
            market_type = get_market_type_from_code(ts_code)
            limit_dict = {
                'MAIN': 9.8,
                'CHINEXT': 19.8,
                'STAR': 19.8,
                'BSE': 29.8
            }
            return limit_dict.get(market_type, 9.8)
        
        def denormalize_regression_predictions(normalized_values, median, iqr, ts_code=None):
            """反标准化回归预测值 - 修复版本"""
            result = (normalized_values * iqr) + median
            
            if isinstance(result, (int, float)) and ts_code is not None:
                max_limit = get_max_return(ts_code)
                min_limit = -max_limit
                
                if result < min_limit or result > max_limit:
                    market_type = get_market_type_from_code(ts_code)
                    logging.warning(f"反标准化结果异常: {result:.4f}% (股票{ts_code}, {market_type}板块, 限制±{max_limit:.1f}%)")
                    
                    if iqr > 0:
                        result = normalized_values * iqr * 0.5 + median
                        result = max(min_limit, min(max_limit, result))
                    else:
                        conservative_change = max_limit * 0.3
                        result = normalized_values * conservative_change + 1.0
                        result = max(min_limit, min(max_limit, result))
            
            return result
        
        # 测试用例：不同板块的极端值处理
        test_cases = [
            # (normalized_value, median, iqr, ts_code, expected_behavior, description)
            (5.0, 2.0, 3.0, '000001.SZ', 'clipped', '主板股票超出限制'),
            (2.0, 1.0, 8.0, '300001.SZ', 'normal', '创业板股票正常范围'),
            (10.0, 1.0, 3.0, '688001.SH', 'clipped', '科创板股票超出限制'),
            (15.0, 2.0, 2.0, '430001.BJ', 'clipped', '北交所股票超出限制'),
            (1.0, 0.0, 5.0, '600000.SH', 'normal', '主板股票正常范围'),
        ]
        
        all_passed = True
        for normalized_val, median, iqr, ts_code, expected_behavior, description in test_cases:
            result = denormalize_regression_predictions(normalized_val, median, iqr, ts_code)
            max_limit = get_max_return(ts_code)
            
            # 检查结果是否在合理范围内
            if -max_limit <= result <= max_limit:
                logging.info(f"✅ {description}: {result:.2f}% (限制±{max_limit:.1f}%)")
            else:
                logging.error(f"❌ {description}: {result:.2f}% 超出限制±{max_limit:.1f}%")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    logging.info("🔧 测试4: 向后兼容性")
    
    try:
        # 模拟不传ts_code的情况
        def denormalize_regression_predictions_old(normalized_values, median, iqr, ts_code=None):
            result = (normalized_values * iqr) + median
            
            if isinstance(result, (int, float)) and ts_code is None:
                # 兼容性：如果没有提供ts_code，使用主板的限制作为默认值
                if result < -9.8 or result > 9.8:
                    logging.warning(f"反标准化结果异常(无股票代码): {result:.4f}% (使用主板限制±9.8%)")
                    if iqr > 0:
                        result = normalized_values * iqr * 0.5 + median
                        result = max(-9.8, min(9.8, result))
                    else:
                        result = normalized_values * 3.0 + 1.0
                        result = max(-9.8, min(9.8, result))
            
            return result
        
        # 测试不传ts_code的情况
        test_cases = [
            (5.0, 2.0, 3.0, '超出主板限制的情况'),
            (1.0, 0.0, 5.0, '正常范围的情况'),
            (-3.0, 1.0, 4.0, '负值情况'),
        ]
        
        all_passed = True
        for normalized_val, median, iqr, description in test_cases:
            result = denormalize_regression_predictions_old(normalized_val, median, iqr)
            
            # 检查结果是否在主板限制范围内
            if -9.8 <= result <= 9.8:
                logging.info(f"✅ {description}: {result:.2f}% (主板限制±9.8%)")
            else:
                logging.error(f"❌ {description}: {result:.2f}% 超出主板限制±9.8%")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def test_edge_cases():
    """测试边界情况"""
    logging.info("🔧 测试5: 边界情况")
    
    try:
        # 测试各种边界情况
        test_cases = [
            (0.0, 0.0, 1.0, '零值情况'),
            (float('inf'), 1.0, 2.0, '无穷大值'),
            (float('-inf'), 1.0, 2.0, '负无穷大值'),
            (np.nan, 1.0, 2.0, 'NaN值'),
            (1.0, 0.0, 0.0, 'IQR为零'),
        ]
        
        def safe_denormalize(normalized_values, median, iqr):
            try:
                result = (normalized_values * iqr) + median
                if np.isnan(result) or np.isinf(result):
                    return 0.0  # 安全的默认值
                return result
            except:
                return 0.0
        
        all_passed = True
        for normalized_val, median, iqr, description in test_cases:
            try:
                result = safe_denormalize(normalized_val, median, iqr)
                if np.isfinite(result):
                    logging.info(f"✅ {description}: {result:.2f}%")
                else:
                    logging.warning(f"⚠️ {description}: 返回非有限值，已处理")
            except Exception as e:
                logging.error(f"❌ {description}: 异常 {str(e)}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logging.info("🚀 开始P.pull.py 标准化和反标准化修复验证测试")
    logging.info("=" * 60)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("市场类型检测功能", test_market_type_detection),
        ("最大涨跌幅计算", test_max_return_calculation),
        ("反标准化涨跌幅限制", test_denormalization_with_limits),
        ("向后兼容性", test_backward_compatibility),
        ("边界情况处理", test_edge_cases)
    ]
    
    for test_name, test_func in tests:
        logging.info(f"\n📋 执行测试: {test_name}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                logging.info(f"✅ {test_name} - 通过")
            else:
                logging.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logging.error(f"❌ {test_name} - 异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 汇总结果
    logging.info("\n" + "=" * 60)
    logging.info("📊 测试结果汇总:")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logging.info(f"  {test_name}: {status}")
    
    logging.info(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        logging.info("🎉 所有测试通过！标准化和反标准化修复成功！")
        logging.info("✅ P.pull.py现在正确使用A股板块涨跌幅限制规则")
        logging.info("✅ 主板: ±9.8%, 创业板/科创板: ±19.8%, 北交所: ±29.8%")
        return True
    else:
        logging.error(f"⚠️ {total - passed}个测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
